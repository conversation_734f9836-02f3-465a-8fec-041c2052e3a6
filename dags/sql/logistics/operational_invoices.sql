
CREATE TABLE IF NOT EXISTS logistics.operational_invoices (
    invoice_id VARCHAR(255),
    customer_purchase_order_id VARCHAR(255),
    customer_order_book_id VARCHAR(255),
    customer_name VARCHAR(255),
    customer_dispatch_order_id VARCHAR(255),
    invoice_type VARCHAR(255),
    invoice_number VARCHAR(255),
    invoice_date TIMESTAMP,
    invoice_amount_without_tax_usd NUMERIC,
    currency_type VARCHAR(255),
    due_date TIMESTAMP,
    tax_amount_usd NUMERIC,
    invoice_quantity NUMERIC,
    allocated_amount_usd NUMERIC,
    total_amount_usd NUMERIC,
    reason_for_deviation VARCHAR(255),
    remarks VARCHAR(255),
    invoice_file_id VARCHAR(255),
    conversion_rate NUMERIC,
    supplier_name VARCHAR(255),
    supplier_order_id VARCHAR(255),
    chemical_name VARCHAR(255),
    custom_cost_description VARCHAR(255),
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    PRIMARY KEY (invoice_id, invoice_number)
);