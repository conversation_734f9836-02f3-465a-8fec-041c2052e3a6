import logging
import sys,os
sys.path.insert(0, '/opt/airflow/dag_dependencies')
from concurrent.futures import ThreadPoolExecutor
from contextlib import contextmanager
from datetime import datetime, timedelta, timezone
from typing import List, Dict, Any, Optional, Tuple

from airflow import DAG
from airflow.operators.python import PythonOperator

from config.db_config import DatabaseConfig
from config.supabase_config import SupabaseConfig
from config.default import DEFAULT_DAG_ARGS, QueryRun  # Changed from dags.config.default
from db.postgres_operations import PostgresOperations
from db.supabase_operations import SupabaseOperations
# Local modules
from failure_task import send_slack_alert
from exception_task_email_dag import send_exception_slack_alert


# Constants
BATCH_SIZE = 250  # Increased batch size for better performance
MAX_WORKERS = 4  # Number of worker threads for parallel processing
DEFAULT_START_DATE = '2023-01-01 00:00:00'
TABLE_NAME = 'public.enquiries'
DOCUMENTS_TABLE_NAME = 'public.enquiry_documents'
COLUMNS = [
    "id", "chemical_name", "brand", "product", "cas_number", "application",
    "country", "target_price", "quantity", "annual_procurement_scale",
    "sales_team_member", "sales_agent_id", "customer_full_name",
    "customer_company", "customer_email", "customer_phone",
    "target_quotation_date", "current_status", "created_at",
    "last_status_change", "sampling_required", "confidence", "category",
    "incoterms", "remarks", "industries" , "is_new", "target_price_currency", "destination","destination_country","enquiry_id",
    "quantity_unit","procurement_unit","expected_procurement_unit", "expected_procurement_volume", "procurement_volume",
    "packaging_type", "qty_per_packaging", "qty_per_packaging_unit"
]
ENQUIRY_DOCUMENT_COLUMNS = [
    "id", "enquiry_id", "draft_id", "file_name", "file_path",
    "content_type", "size", "uploaded_by", "created_at", "document_type"
]


class EnquiriesSyncManager:
    """Manager class for enquiries synchronization"""

    def __init__(self):
        self.src_db: Optional[PostgresOperations] = None
        self.dst_db: Optional[PostgresOperations] = None
        self.src_supabase: Optional[SupabaseOperations] = None
        self.dst_supabase: Optional[SupabaseOperations] = None
        self.columns = COLUMNS
        self.columns_str = ", ".join(self.columns)
        self.enquiry_document_columns = ENQUIRY_DOCUMENT_COLUMNS
        self.enquiry_document_columns_str = ", ".join(self.enquiry_document_columns)
        self.sales_enquiry_documents_bucket = SupabaseConfig.STORAGE_BUCKETS['SALES_STACK']['ENQUIRY_DOCUMENTS']
        self.procure_enquiry_documents_bucket = SupabaseConfig.STORAGE_BUCKETS['PROCURE_STACK']['ENQUIRY_DOCUMENTS']
        logging.warning(f"Fetching from {QueryRun}")

    @contextmanager
    def database_connections(self):
        """Context manager for database connections"""
        try:
            sales_params = DatabaseConfig.get_sales_stack_postgres_params()
            procure_params = DatabaseConfig.get_procuro_stack_postgres_params()

            self.src_db = PostgresOperations(sales_params)
            self.dst_db = PostgresOperations(procure_params)

            sales_supabase_params = SupabaseConfig.get_sales_stack_supabase_params()
            procure_supabase_params = SupabaseConfig.get_procure_stack_supabase_params()

            # Pass bucket name to SupabaseOperations
            sales_supabase_params['bucket_name'] = self.sales_enquiry_documents_bucket
            procure_supabase_params['bucket_name'] = self.procure_enquiry_documents_bucket

            self.src_supabase = SupabaseOperations(sales_supabase_params)
            self.dst_supabase = SupabaseOperations(procure_supabase_params)

            logging.info(f"Connected to databases - Sales: {sales_params['host']}, Procure: {procure_params['host']}")
            yield

        finally:
            if self.src_db:
                self.src_db.close()
            if self.dst_db:
                self.dst_db.close()
            logging.info("Database connections closed")

    def get_last_sync_time(self) -> str:
        """Get the last synchronization timestamp"""
        query = f"SELECT MAX(created_at) as last_created_at FROM {TABLE_NAME}"
        result = self.dst_db.read_data(query)
        return result[0]['last_created_at'].strftime('%Y-%m-%d %H:%M:%S') if result and result[0][
            'last_created_at'] else DEFAULT_START_DATE

    def fetch_new_enquiry_documents(self, last_sync_time: str) -> List[Dict[str, Any]]:
        """Fetch new records from source database"""
        query = f"""
            SELECT {self.enquiry_document_columns_str}
            FROM {DOCUMENTS_TABLE_NAME}
            WHERE created_at > :last_sync_time
            ORDER BY created_at
        """
        return self.src_db.read_data(query, {"last_sync_time": last_sync_time})

    def fetch_new_enquiries(self, last_sync_time: str) -> List[Dict[str, Any]]:
        """Fetch new records from source database"""
        query = f"""
            WITH ranked_records AS (
                SELECT 
                    enq.id, enq.chemical_name, enq.brand, enq.product, 
                    enq.cas_number, enq.application, enq.country, 
                    enq.target_price, enq.quantity, enq.annual_procurement_scale, 
                    enq.sales_team_member, enq.sales_agent_id, 
                    cus.customer_full_name, cus.customer_company, 
                    cus.customer_email, cus.customer_phone,
                    enq.target_quotation_date, 'enquiry_received' as current_status, 
                    enq.created_at, enq.last_status_change, 
                    enq.sampling_required, enq.confidence, enq.category, 
                    enq.incoterms, enq.remarks, enq.industries, enq.is_new, enq.target_price_currency,
                    enq.destination,enq.destination_country,enq.enquiry_id,
                    enq.quantity_unit,enq.procurement_unit, enq.expected_procurement_unit,
                    enq.expected_procurement_volume, enq.procurement_volume,
                    enq.packaging_type, enq.qty_per_packaging, enq.qty_per_packaging_unit,
                    ROW_NUMBER() OVER (PARTITION BY enq.id ORDER BY enq.created_at DESC) as rn
                FROM 
                    public.enquiries AS enq 
                INNER JOIN 
                    public.customer AS cus ON enq.customer_id = cus.id
                WHERE 
                    enq.created_at > :last_sync_time or enq.updated_on > :last_sync_time
            )
            SELECT * FROM ranked_records WHERE rn = 1
            ORDER BY created_at
        """
        return self.src_db.read_data(query, {"last_sync_time": last_sync_time})

    def sync_status_changes(self, enquiry_id: str, **kwargs) -> bool:
        """Sync status changes from sales to procure stack"""
        try:
            # First fetch status history from sales stack
            select_query = """
                SELECT 
                    id,
                    enquiry_id,
                    status as previous_state,
                    notes,
                    created_at,
                    changed_by
                FROM public.enquiry_status_history 
                WHERE enquiry_id = :enquiry_id and status = 'enquiry_created'
                ORDER BY created_at
            """

            status_history = self.src_db.read_data(select_query, {"enquiry_id": enquiry_id})

            if not status_history:
                logging.info(f"No status history found for enquiry {enquiry_id}")
                return True

            # Insert into procure stack status changes
            insert_query = """
                INSERT INTO public.status_changes (
                    id,
                    enquiry_id,
                    previous_state,
                    new_state,
                    notes,
                    created_at,
                    created_by,
                    response,
                    response_date
                )
                VALUES (
                    :id,
                    :enquiry_id,
                    :previous_state,
                    'enquiry_received',
                    :notes,
                    :created_at,
                    :created_by,
                    '',
                    CURRENT_TIMESTAMP
                )
                ON CONFLICT (id) DO NOTHING
            """

            for record in status_history:
                params = {
                    "id": record["id"],
                    "enquiry_id": record["enquiry_id"],
                    "previous_state": record["previous_state"],
                    "notes": record["notes"],
                    "created_at": record["created_at"],
                    "created_by": record["changed_by"]
                }

                self.dst_db.write_data(insert_query, params)

            logging.info(f"Successfully synced status changes for enquiry {enquiry_id}")
            return True

        except Exception as e:
            logging.error(f"Error syncing status changes for enquiry {enquiry_id}: {str(e)}")
            send_exception_slack_alert(e, context_info=kwargs)
            return False

    def process_batch(self, records: List[Dict[str, Any]], table_name: str, column_list: List[str], **kwargs) -> int:
        """Process a batch of records"""
        try:
            if not records:
                return 0

            column_str = ", ".join(column_list)
            placeholders = [f":p_{idx}" for idx in range(len(column_list))]
            values_list = []
            params = {}

            for idx, record in enumerate(records):
                record_params = {f"p_{i}": record.get(col) for i, col in enumerate(column_list)}
                params.update({f"{k}_{idx}": v for k, v in record_params.items()})
                values_list.append(f"({', '.join(f':p_{i}_{idx}' for i in range(len(column_list)))})")

                # Construct update part for UPSERT operation
                update_columns = [col for col in column_list if col != 'id' and col != 'current_status'
]
                update_parts = [f"{col} = EXCLUDED.{col}" for col in update_columns]
                update_str = ", ".join(update_parts)

                insert_sql = f"""
                    INSERT INTO {table_name} ({column_str})
                    VALUES {', '.join(values_list)}
                    ON CONFLICT (id) DO UPDATE SET 
                    {update_str}
                """

                self.dst_db.write_data(insert_sql, params)

                # After successful insert/update, sync status changes for each enquiry
            if table_name == TABLE_NAME:  # Only for enquiries table
                for record in records:
                    self.sync_status_changes(record["id"])

            return len(records)
        except Exception as e:
            logging.error(f"Error processing batch: {str(e)}")
            send_exception_slack_alert(e, context_info=kwargs)
            return 0

    def process_document(self, record: Dict[str, Any], **kwargs) -> bool:
        """Process a single document record by transferring it between Supabase instances"""
        try:
            file_path = record['file_path']

            # Check if file exists in destination using the check_file_exists method
            if self.dst_supabase.check_file_exists(file_path):
                logging.info(f"File already exists, skipping: {file_path}")
                return True

            # Use transfer_file method to handle download and upload
            if self.src_supabase.transfer_file(file_path, self.dst_supabase, record['content_type']):
                logging.info(f"Successfully processed file: {file_path}")
                return True

            logging.error(f"Failed to transfer file: {file_path}")
            return False

        except Exception as e:
            logging.error(f"Error processing document ID {record.get('id')}: {str(e)}")
            send_exception_slack_alert(e, context_info=kwargs)
            return False

    def process_documents_batch(self, documents: List[Dict[str, Any]], **kwargs) -> Tuple[int, List[Dict[str, Any]]]:
        """Process a batch of documents in parallel"""
        success_count = 0
        failed_documents = []

        with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
            future_to_doc = {executor.submit(self.process_document, doc): doc for doc in documents}

            for future in future_to_doc:
                doc = future_to_doc[future]
                try:
                    if future.result():
                        success_count += 1
                    else:
                        failed_documents.append(doc)
                except Exception as e:
                    logging.error(f"Error processing document {doc.get('id')}: {str(e)}")
                    failed_documents.append(doc)
                    send_exception_slack_alert(e, context_info=kwargs)

        return success_count, failed_documents


def sync_enquiries_data() -> None:
    """Main synchronization function"""
    sync_manager = EnquiriesSyncManager()

    with sync_manager.database_connections():
        #last_sync_time = sync_manager.get_last_sync_time()
        logging.info(f"Fetching records created or updated after: {QueryRun}")

        new_records = sync_manager.fetch_new_enquiries(QueryRun)
        new_enquiries_document = sync_manager.fetch_new_enquiry_documents(QueryRun)

        record_count = len(new_records)
        document_count = len(new_enquiries_document)

        if not record_count:
            logging.info("No new or updated enquiries to sync")
            return

        logging.info(f"Found {record_count} enquiries (new or updated) and {document_count} documents to sync")

        # Process enquiries in batches
        success_count = 0
        for i in range(0, len(new_records), BATCH_SIZE):
            batch = new_records[i:i + BATCH_SIZE]
            success_count += sync_manager.process_batch(batch, TABLE_NAME, COLUMNS)
            logging.info(f"Processed batch of {len(batch)} enquiries")

        # Process documents in batches
        document_success_count = 0
        remaining_documents = []

        for i in range(0, len(new_enquiries_document), BATCH_SIZE):
            batch = new_enquiries_document[i:i + BATCH_SIZE]
            success, failed = sync_manager.process_documents_batch(batch)
            document_success_count += success
            remaining_documents.extend(failed)

            # Process successful documents in database
            successful_docs = [doc for doc in batch if doc not in failed]
            if successful_docs:
                sync_manager.process_batch(successful_docs, DOCUMENTS_TABLE_NAME, ENQUIRY_DOCUMENT_COLUMNS)

            logging.info(f"Processed batch of {len(batch)} documents")

        # Log final statistics
        logging.info(f"Sync completed: {success_count}/{record_count} enquiries synced successfully")
        logging.info(f"Documents processed: {document_success_count}/{document_count} successfully")
        if remaining_documents:
            logging.warning(f"{len(remaining_documents)} documents remained unprocessed")



# def fail_this_task():
#     raise Exception("This is a test failure to trigger SES email alert")

# Create the DAG
dag = DAG(
    'sales_to_procure_only_enquiries_sync',
    default_args={
        **DEFAULT_DAG_ARGS,
        'on_failure_callback':send_slack_alert # Add recipient email address(es) here
    },
    description='Sync enquiries and their documents from SalesStack to ProcureStack',
    schedule=timedelta(minutes=3),
    catchup=False,
    start_date=datetime(2024, 1, 1, tzinfo=timezone.utc),
    tags=['database', 'sync', 'postgres', 'enquiries', 'documents'],
    dagrun_timeout=timedelta(minutes=10),  # Auto-fail after 10 minutes
    max_active_runs=1  # No concurrent runs
)


# Create the enquiries sync task
sync_enquiries_task = PythonOperator(
    task_id='sync_enquiries',
    python_callable=sync_enquiries_data,
    dag=dag,
    retries=0,
    retry_delay=timedelta(minutes=5),
    execution_timeout=timedelta(minutes=5)
)
