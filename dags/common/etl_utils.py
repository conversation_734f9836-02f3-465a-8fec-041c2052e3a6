sales_etl_dtypes = {
    "account_planning": {
        "id": "string",
        "customer_id": "string",
        "offset_chemical_id": "string",
        "offset_chemical_name": "string",
        "chemical_name": "string",
        "grade": "string",
        "quarterly_volume": "float64",
        "status": "string",
        "quarterly_value": "float64",
        "previous_quarter_value": "float64",
        "target_this_quarter": "float64",
        "conversion_probability": "float64",  
        "comments": "string",
        "created_at": "datetime64[ns]",
        "updated_at": "datetime64[ns]",
    },
    "account_planning_history": {
        "id": "string",
        "account_planning_id": "string",
        "customer_id": "string",
        "offset_chemical_id": "string",
        "offset_chemical_name": "string",
        "chemical_name": "string",
        "grade": "string",
        "quarterly_volume": "float64",
        "status": "string",
        "quarterly_value": "float64",
        "previous_quarter_value": "float64",
        "target_this_quarter": "float64",
        "conversion_probability": "float64",  
        "comments": "string",
        "created_at": "datetime64[ns]",
        "updated_at": "datetime64[ns]",
    },
    "customer": {
        "id": "string",
        "customer_full_name": "string",
        "customer_company": "string",
        "customer_phone": "string",
        "customer_email": "string",
        "account_owner": "string",
        "address": "string",
        "city": "string",
        "state": "string",
        "country": "string",
        "type": "string",
        "size": "string",
        "created_at": "datetime64[ns]",
        "created_by": "string",
        "modified_at": "datetime64[ns]",
        "modified_by": "string",
        "industries": "string",
        "customer_id": "string",
        "postal_code": "string",
        "customer_poc": "string",
        "annual_turnover" : "string",
        "annual_chemicals_procurement": "string"
    },
    "customer_quotations": {
        "id": "string",
        "created_at": "datetime64[ns]",
        "updated_at": "datetime64[ns]",
        "created_by": "string",
        "document_file_path": "string",
        "document_file_name": "string",
    },
    "enquiries": {
        "id": "string",
        "enquiry_id":"string",
        "chemical_name": "string",
        "brand": "string",
        "product": "string",
        "cas_number": "string",
        "country": "string",
        "target_price": "float64",
        "quantity": "float64",
        "created_at": "datetime64[ns]",
        "sales_team_member": "string",
        "notes": "string",
        "sales_agent_id": "string",
        "description": "string",
        "draft_id": "string",
        "sample_requested": "bool",
        "sample_requested_at": "datetime64[ns]",
        "current_status": "string",
        "last_status_change": "datetime64[ns]",
        "target_quotation_date": "datetime64[ns]",
        "confidence": "string",
        "application": "string",
        "category": "string",
        "incoterms": "string",
        "remarks": "string",
        "annual_procurement_scale": "float64",
        "sampling_required": "bool",
        "expected_procurement_volume": "float64",
        "industries": "string",
        "procurement_volume": "float64",
        "city": "string",
        "expected_procurement_unit": "string",
        "procurement_unit": "string",
        "quantity_unit": "string",
        "customer_id": "string",
        "customer_full_name": "string",
        "is_new": "bool",
        "target_price_currency": "string",
        "destination": "string",
        "destination_country" : "string",
        "updated_on": "datetime64[ns]",
    },
    "enquiry_clarifications": {
        "id": "string",
        "enquiry_id": "string",
        "query": "string",
        "response": "string",
        "created_at": "datetime64[ns]",
        "resolved_at": "datetime64[ns]",
        "created_by": "string",
        "status": "string",
    },
    "enquiry_documents": {
        "id": "string",
        "enquiry_id": "string",
        "draft_id": "string",
        "file_name": "string",
        "file_path": "string",
        "content_type": "string",
        "size": "int64",
        "uploaded_by": "string",
        "created_at": "datetime64[ns]",
    },
    "enquiry_quotations": {
        "id": "string",
        "enquiry_id": "string",
        "quotation_id": "string",
        "created_at": "datetime64[ns]",
        "is_current": "bool",
    },
    "enquiry_status_history": {
        "id": "string",
        "enquiry_id": "string",
        "status": "string",
        "notes": "string",
        "changed_by": "string",
        "created_at": "datetime64[ns]",
        "sales_agent_email": "string",
        "procurement_poc": "string",
    },
    "purchase_order_attachments": {
        "id": "string",
        "purchase_order_id": "string",
        "file_name": "string",
        "file_path": "string",
        "content_type": "string",
        "size": "int64",
        "created_at": "datetime64[ns]",
        "doc_type": "string",
    },
    "purchase_orders": {
        "id": "string",
        "enquiry_id": "string",
        "created_by": "string",
        "created_at": "datetime64[ns]",
        "notes": "string",
        "status": "string",
        "po_number": "string",
        "customer_id": "string",
        "history_status_id": "string",
        "frequency": "string",
        'po_value' : 'float',
        "po_currency" : 'string'
    },
    "purchase_order_items": {
        "id": "string",                     
        "purchase_order_id": "string",      
        "chemical_name": "string",          
        "po_value": "float",                
        "po_currency": "string",           
        "frequency": "string",             
        "remarks": "string",                
        "created_at": "datetime64[ns]",     
        "updated_at": "datetime64[ns]",     
        "enquiry_id": "string",             
        "history_status_id": "string"       
    },
    "quotation": {
        "id": "string",
        "enquiry_id": "string",
        "quote_preparation_id": "string",
        "generated_by": "string",
        "generated_at": "datetime64[ns]",
        "file_path": "string",
        "notes": "string",
        "status_change_id": "string",
    },
    "quotation_feedback": {
        "id": "string",
        "enquiry_id": "string",
        "response": "string",
        "reason": "string",
        "submitted_by": "string",
        "created_at": "datetime64[ns]",
        "pricing_quote_id": "string",
        "remarks": "string",
    },
    "quotation_feedback_attachments": {
        "id": "string",
        "feedback_id": "string",
        "file_name": "string",
        "file_path": "string",
        "content_type": "string",
        "size": "int64",
        "created_at": "datetime64[ns]",
    },
    "quote_generation_attachments": {
        "id": "string",
        "size": "int64",
        "created_at": "datetime64[ns]",
        "file_path": "string",
        "file_name": "string",
        "content_type": "string",
        "quote_generation_id": "string",
    },
    "quote_generation_details": {
        "id": "string",
        "enquiry_id": "string",
        "generated_by": "string",
        "generated_at": "datetime64[ns]",
        "pdf_file_path": "string",
        "notes": "string",
        "status_history_id": "string",
        "customer_name": "string",
        "product": "string",
        "packaging": "string",
        "price": "float64",
        "price_unit_of_measure": "string",
        "currency": "string",
        "quantity": "float64",
        "quantity_unit_of_measure": "string",
        "amount": "float64",
        "po_delivery_time": "datetime64[ns]",
        "expiry_date": "datetime64[ns]",
        "inco_terms": "string",
        "payment_terms": "string",
        "cas": "string",
    },
    "quote_generation_options": {
        "id": "string",
        "quote_generation_id": "string",
        "customer_name": "string",
        "product_name": "string",
        "packaging_name": "string",
        "price": "float64",
        "currency": "string",
        "quantity": "float64",
        "unit": "string",
        "amount": "float64",
        "po_to_delivery_time": "int64",
        "expiry_date": "datetime64[ns]",
        "created_at": "datetime64[ns]",
        "updated_at": "datetime64[ns]",
        "cas": "string",
    },
    "sample_feedback": {
        "id": "string",
        "enquiry_id": "string",
        "response": "string",
        "reason": "string",
        "submitted_by": "string",
        "created_at": "datetime64[ns]",
        "type": "string",
        "feedback_history_id": "string",
        "remarks": "string",
    },
    "sample_feedback_attachments": {
        "id": "string",
        "feedback_id": "string",
        "file_name": "string",
        "file_path": "string",
        "content_type": "string",
        "size": "int64",
        "created_at": "datetime64[ns]",
    },
    "sample_requests": {
        "id": "string",
        "enquiry_id": "string",
        "quantity": "float64",
        "quantity_unit": "string",
        "delivery_address": "string",
        "delivery_city": "string",
        "delivery_country": "string",
        "delivery_postal_code": "string",
        "remarks": "string",
        "created_at": "datetime64[ns]",
        "created_by": "string",
        "status": "string",
        "sales_team_member": "string",
        "status_history_id": "string",
        "tracking_number": "string",
        "expected_delivery_date": "string",
        "carrier_name": "string",
        "sample_poc": "string",
        "contact_email": "string",
        "contact_phone": "string",
        "tracking_url": "string",
        'last_status_change': "datetime64[ns]",
    },
    "sample_requests_attachments": {
        "id": "string",
        "sample_request_id": "string",
        "file_name": "string",
        "file_path": "string",
        "content_type": "string",
        "size": "int64",
        "created_at": "datetime64[ns]",
    },
    "sample_status_history": {
        "id": "string",
        "sample_request_id": "string",
        "sample_status": "string",
        "changed_at": "datetime64[ns]",
        'enquiry_id': "string",
    },
    "user_roles": {
        "id": "string",
        "user_id": "string",
        "role": "string",
        "created_at": "datetime64[ns]",
        "category": "string",
        "country": "string",
    },
    "users": {
        "instance_id": "string",
        "id": "string",
        "aud": "string",
        "role": "string",
        "email": "string",
        "encrypted_password": "string",
        "email_confirmed_at": "datetime64[ns]",
        "invited_at": "datetime64[ns]",
        "confirmation_token": "string",
        "confirmation_sent_at": "datetime64[ns]",
        "recovery_token": "string",
        "recovery_sent_at": "datetime64[ns]",
        "email_change_token_new": "string",
        "email_change": "string",
        "email_change_sent_at": "datetime64[ns]",
        "last_sign_in_at": "datetime64[ns]",
        "is_super_admin": "bool",
        "created_at": "datetime64[ns]",
        "updated_at": "datetime64[ns]",
        "phone": "string",
        "phone_confirmed_at": "datetime64[ns]",
        "phone_change": "string",
        "phone_change_token": "string",
        "phone_change_sent_at": "datetime64[ns]",
        "confirmed_at": "datetime64[ns]",
        "email_change_token_current": "string",
        "email_change_confirm_status": "int64",
        "banned_until": "datetime64[ns]",
        "reauthentication_token": "string",
        "reauthentication_sent_at": "datetime64[ns]",
        "is_sso_user": "bool",
        "deleted_at": "datetime64[ns]",
        "is_anonymous": "bool",
    },
    "customer_meeting": {
        "id": "string",
        "title": "string",
        "customer_id": "string",
        "account_owner": "string",
        "summary": "string",
        "date": "datetime64[ns]",
        "meeting_type": "string",
        "created_at": "datetime64[ns]",
        "updated_at": "datetime64[ns]",
        "follow_up_steps": "string",
    }
}


procure_etl_dtypes = {
    "audit_logs": {
        "id": "string",
        "user_id": "string",
        "action": "string",
        "entity_type": "string",
        "entity_id": "string",
        "old_values": "object",
        "new_values": "object",
        "ip_address": "string",
        "timestamp": "datetime64[ns]",
    },
    "profiles" : {
        "id": "string",
        "full_name": "string",
        "email": "string",
        "role": "string",
        "status": "string",
        "created_at": "datetime64[ns]",
        "role_code": "string",
        "business_unit_id": "string",
        "assigned_enquiries": "int64",
        "last_active": "datetime64[ns]",
    },
    "business_units": {"id": "string", "name": "string"},
    "chemical_applications": {
        "id": "string",
        "chemical_id": "string",
        "application": "string",
    },
    "chemical_categories": {"id": "string", "name": "string", "description": "string"},
    "chemical_safety_data": {
        "id": "string",
        "chemical_id": "string",
        "msds_url": "string",
        "safety_notes": "string",
        "handling_instructions": "string",
        "regulatory_info": "object",
    },
    "chemical_synonyms": {"id": "string", "chemical_id": "string", "synonym": "string"},
    "chemicals": {
        "id": "string",
        "chemical_name": "string",
        "category_id": "string",
        "category": "string",
        "product_family": "string",
        "cas_number": "string",
        "grade": "string",
        "molecular_formula": "string",
        "molecular_weight": "float64",
        "purity": "string",
        "hazard_classification": "string",
        "storage_requirements": "string",
        "shelf_life": "string",
        "created_at": "datetime64[ns]",
        "created_by": "string",
        "updated_at": "datetime64[ns]",
        "is_active": "bool",
        "product_id": "string",
        "technical_name": "string",
        "sub_category": "string",
        "updated_by": "string",
        "functions": "string",
    },
    "dashboard_configs": {
        "id": "string",
        "user_id": "string",
        "layout": "object",
        "created_at": "datetime64[ns]",
        "updated_at": "datetime64[ns]",
    },
    "enquiries": {
        "id": "string",
        "enquiry_id":"string",
        "chemical_name": "string",
        "brand": "string",
        "product": "string",
        "cas_number": "string",
        "application": "string",
        "country": "string",
        "target_price": "float64",
        "quotation_location": "string",
        "quantity": "float64",
        "annual_procurement_scale": "float64",
        "sales_team_member": "string",
        "sales_agent_id": "string",
        "customer_full_name": "string",
        "customer_company": "string",
        "customer_email": "string",
        "customer_phone": "string",
        "target_quotation_date": "datetime64[ns]",
        "current_status": "string",
        "created_at": "datetime64[ns]",
        "last_status_change": "datetime64[ns]",
        "sampling_required": "bool",
        "confidence": "string",
        "category": "string",
        "incoterms": "string",
        "remarks": "string",
        "industries": "string",
        "procurement_poc": "string",
        "procurement_poc_id": "string",
        "is_new": "bool",
        "target_price_currency": "string",
        "destination": "string",
        "chemical_id": "string",
        "expected_procurement_unit": "string",
        "procurement_unit": "string",
        "quantity_unit": "string",
        "expected_procurement_volume": "float64",
        "procurement_volume": "float64",
        "packaging_type": "string",
        "qty_per_packaging": "float64",
        "qty_per_packaging_unit": "string",
        "destination_country" : "string",
    },
    "enquiry_clarifications": {
        "id": "string",
        "enquiry_id": "string",
        "query": "string",
        "response": "string",
        "created_at": "datetime64[ns]",
        "resolved_at": "datetime64[ns]",
        "created_by": "string",
        "status": "string",
    },
    "enquiry_documents": {
        "id": "string",
        "enquiry_id": "string",
        "draft_id": "string",
        "file_name": "string",
        "file_path": "string",
        "content_type": "string",
        "size": "int64",
        "uploaded_by": "string",
        "created_at": "datetime64[ns]",
    },
    "pipeline_stages": {
        "code": "string",
        "title": "string",
        "description": "string",
        "order_index": "int64",
        "expected_duration": "int64",
        "is_active": "bool",
    },
    "po_deliveries": {
        "id": "string",
        "po_id": "string",
        "delivery_date": "datetime64[ns]",
        "status": "string",
        "tracking_number": "string",
        "carrier": "string",
        "received_by": "string",
        "received_date": "datetime64[ns]",
        "quantity_received": "float64",
        "quality_check_status": "string",
        "notes": "string",
    },
    "po_items": {
        "id": "string",
        "po_id": "string",
        "chemical_id": "string",
        "description": "string",
        "quantity": "float64",
        "unit": "string",
        "unit_price": "float64",
        "line_total": "float64",
        "delivery_date": "datetime64[ns]",
    },
    "profiles": {
        "id": "string",
        "full_name": "string",
        "email": "string",
        "role": "string",
        "status": "string",
        "created_at": "datetime64[ns]",
        "role_code": "string",
        "business_unit_id": "string",
        "assigned_enquiries": "int64",
        "last_active": "datetime64[ns]",
    },
    "purchase_order_attachments": {
        "id": "string",
        "purchase_order_id": "string",
        "file_name": "string",
        "file_path": "string",
        "content_type": "string",
        "size": "int64",
        "created_at": "datetime64[ns]",
        "doc_type": "string",
    },
    "purchase_orders": {
        "id": "string",
        "po_number": "string",
        "enquiry_id": "string",
        "quotation_id": "string",
        "supplier_id": "string",
        "order_date": "datetime64[ns]",
        "delivery_date": "datetime64[ns]",
        "status": "string",
        "total_value": "float64",
        "currency": "string",
        "payment_terms": "string",
        "delivery_terms": "string",
        "shipping_address": "object",
        "billing_address": "object",
        "created_by": "string",
        "approved_by": "string",
        "approval_date": "datetime64[ns]",
        "notes": "string",
        "frequency": "string",
        "created_at": "datetime64[ns]",
    },
    "purchase_orders_raised": {
        "id": "string",
        "enquiry_id": "string",
        "po_number": "string",
        "notes": "string",
        "created_by": "string",
        "created_at": "datetime64[ns]",
        "customer_full_name": "string",
        "customer_email": "string",
        "customer_phone": "string",
        "customer_company": "string",
        "status_change_id": "string",
        "frequency": "string",
        'po_value' : 'float',
        "po_currency" : 'string',
    },
    "purchase_order_items": {
        "id": "string",                     
        "purchase_order_id": "string",      
        "chemical_name": "string",          
        "po_value": "float",                
        "po_currency": "string",           
        "frequency": "string",             
        "remarks": "string",                
        "created_at": "datetime64[ns]",     
        "updated_at": "datetime64[ns]",     
        "enquiry_id": "string",             
        "history_status_id": "string"       
    },
    "quotation_approvals": {
        "id": "string",
        "quotation_id": "string",
        "approver_id": "string",
        "status": "string",
        "approval_date": "datetime64[ns]",
        "comments": "string",
        "approval_level": "int64",
    },
    "quotation_feedback": {
        "id": "string",
        "enquiry_id": "string",
        "response": "string",
        "reason": "string",
        "submitted_by": "string",
        "created_at": "datetime64[ns]",
        "status_change_id": "string",
        "remarks": "string",
    },
    "quotation_feedback_attachments": {
        "id": "string",
        "feedback_id": "string",
        "file_name": "string",
        "file_path": "string",
        "content_type": "string",
        "size": "int64",
        "created_at": "datetime64[ns]",
    },
    "quotation_items": {
        "id": "string",
        "quotation_id": "string",
        "chemical_id": "string",
        "description": "string",
        "quantity": "float64",
        "unit": "string",
        "unit_price": "float64",
        "line_total": "float64",
        "lead_time": "int64",
    },
    "quotation_preparation_attachments": {
        "id": "string",
        "quote_preparation_id": "string",
        "file_name": "string",
        "file_path": "string",
        "uploaded_by": "string",
        "file_type": "string",
        "file_size": "int64",
        "created_at": "datetime64[ns]",
    },
    "quotations": {
        "id": "string",
        "enquiry_id": "string",
        "supplier_id": "string",
        "quotation_number": "string",
        "prepared_by": "string",
        "prepared_at": "datetime64[ns]",
        "price": "float64",
        "quantity": "float64",
        "unit": "string",
        "currency": "string",
        "delivery_terms": "string",
        "payment_terms": "string",
        "validity_period": "int64",
        "valid_until": "datetime64[ns]",
        "freight_charges": "float64",
        "tax_percentage": "float64",
        "total_value": "float64",
        "status": "string",
        "comments": "string",
        "created_at": "datetime64[ns]",
        "updated_at": "datetime64[ns]",
    },
    "quote_approval_details": {
        "id": "string",
        "enquiry_id": "string",
        "quote_preparation_id": "string",
        "approver": "string",
        "approved_at": "datetime64[ns]",
        "comments": "string",
        "status_change_id": "string",
    },
    "quote_attachments": {
        "id": "string",
        "quote_option_id": "string",
        "file_name": "string",
        "file_path": "string",
        "file_type": "string",
        "content_type": "string",
        "size": "int64",
        "created_at": "datetime64[ns]",
    },
    "quote_generation_attachments": {
        "id": "string",
        "quote_generation_id": "string",
        "file_name": "string",
        "file_path": "string",
        "content_type": "string",
        "size": "int64",
        "created_at": "datetime64[ns]",
    },
    "quote_generation_details": {
        "id": "string",
        "enquiry_id": "string",
        "quote_preparation_id": "string",
        "generated_by": "string",
        "generated_at": "datetime64[ns]",
        "pdf_file_path": "string",
        "notes": "string",
        "status_change_id": "string",
        "inco_terms": "string",
        "payment_terms": "string",
    },
    "quote_generation_options": {
        "id": "string",
        "quote_generation_id": "string",
        "customer_name": "string",
        "product_name": "string",
        "packaging_name": "string",
        "price": "float64",
        "currency": "string",
        "quantity": "float64",
        "unit": "string",
        "amount": "float64",
        "po_to_delivery_time": "int64",
        "expiry_date": "datetime64[ns]",
        "created_at": "datetime64[ns]",
        "updated_at": "datetime64[ns]",
        "cas": "string",
    },
    "quote_options": {
        "id": "string",
        "quote_preparation_id": "string",
        "supplier_price": "float64",
        "currency": "string",
        "quantity": "float64",
        "unit": "string",
        "specific_gravity": "float64",
        "first_mile_cost": "float64",
        "original_local_charges": "float64",
        "shipping_charges": "float64",
        "haz_surcharge": "float64",
        "palletization": "float64",
        "tank_charges": "float64",
        "duty_rate": "float64",
        "duty_charges": "float64",
        "destination_local_charges": "float64",
        "door_delivery_charges": "float64",
        "warehouse_cost": "float64",
        "fob_cost_without_margin": "float64",
        "cif_cost_without_margin": "float64",
        "ddp_cost_kg_without_margin": "float64",
        "ddp_cost_lb_without_margin": "float64",
        "margin": "float64",
        "fob_price_with_margin": "float64",
        "cif_price_with_margin": "float64",
        "ddp_price_kg_with_margin": "float64",
        "ddp_price_lb_with_margin": "float64",
        "moq": "string",
        "fcl_load": "string",
        "packaging": "string",
        "haz_type": "string",
        "created_at": "datetime64[ns]",
        "updated_at": "datetime64[ns]",
        "po_to_delivery_time": "int64",
        "cas": "string",
        "expiry_date": "datetime64[ns]",
    },
    "quote_preparation_details": {
        "id": "string",
        "enquiry_id": "string",
        "created_at": "datetime64[ns]",
        "created_by": "string",
        "port": "string",
        "supplier_name": "string",
        "supplier_location": "string",
        "supplier_price": "float64",
        "quantity": "float64",
        "unit": "string",
        "specific_gravity": "float64",
        "first_mile_cost": "float64",
        "original_local_charges": "float64",
        "shipping_charges": "float64",
        "haz_surcharge": "float64",
        "palletization": "float64",
        "tank_charges": "float64",
        "duty_rate": "float64",
        "duty_charges": "float64",
        "destination_local_charges": "float64",
        "door_delivery_charges": "float64",
        "warehouse_cost": "float64",
        "fob_cost_without_margin": "float64",
        "cif_cost_without_margin": "float64",
        "ddp_cost_kg_without_margin": "float64",
        "ddp_cost_lb_without_margin": "float64",
        "margin": "float64",
        "fob_price_with_margin": "float64",
        "cif_price_with_margin": "float64",
        "ddp_price_kg_with_margin": "float64",
        "ddp_price_lb_with_margin": "float64",
        "moq": "float64",
        "fcl_load": "string",
        "packaging": "string",
        "haz_type": "string",
        "attachments": "string",
        "status_change_id": "string",
        "currency": "string",
        "supplier_id": "string",
        "destination": "string",
        "incoterms": "string",
    },
    "quote_preparations": {
        "id": "string",
        "enquiry_id": "string",
        "status_change_id": "string",
        "supplier_id": "string",
        "supplier_name": "string",
        "supplier_location": "string",
        "destination": "string",
        "port": "string",
        "incoterms": "string",
        "created_at": "datetime64[ns]",
        "updated_at": "datetime64[ns]",
        "created_by": "string",
        "paymentterms": "string",
    },
    "roles": {"code": "string", "name": "string"},
    "sample_feedback": {
        "id": "string",
        "enquiry_id": "string",
        "response": "string",
        "reason": "string",
        "submitted_by": "string",
        "created_at": "datetime64[ns]",
        "type": "string",
        "status_change_id": "string",
        "remarks": "string",
    },
    "sample_feedback_attachments": {
        "id": "string",
        "feedback_id": "string",
        "file_name": "string",
        "file_path": "string",
        "content_type": "string",
        "size": "int64",
        "created_at": "datetime64[ns]",
    },
    "sample_initiation_details": {
        "id": "string",
        "enquiry_id": "string",
        "sample_request_id": "string",
        "initiated_by": "string",
        "initiated_at": "datetime64[ns]",
        "supplier_name": "string",
        "supplier_address": "string",
        "status": "string",
        "mrd_date": "datetime64[ns]",
        "expected_delivery_date": "datetime64[ns]",
        "tracking_number": "string",
        "status_change_id": "string",
        "carrier_name": "string",
    },
    "sample_requests": {
        "id": "string",
        "enquiry_id": "string",
        "quantity": "float64",
        "quantity_unit": "string",
        "delivery_address": "string",
        "delivery_city": "string",
        "delivery_country": "string",
        "delivery_postal_code": "string",
        "remarks": "string",
        "created_at": "datetime64[ns]",
        "created_by": "string",
        "status": "string",
        "sales_team_member": "string",
        "status_history_id": "string",
        "contact_email": "string",
        "contact_phone": "string",
        "sample_poc": "string",
        "supplier_id": "string",
        "supplier_name": "string",
        'last_status_change': "datetime64[ns]",
    },
    "sample_requests_attachments": {
        "id": "string",
        "sample_request_id": "string",
        "file_name": "string",
        "file_path": "string",
        "content_type": "string",
        "size": "int64",
        "created_at": "datetime64[ns]",
    },
    "sample_test_results": {
        "id": "string",
        "sample_id": "string",
        "test_date": "datetime64[ns]",
        "tested_by": "string",
        "parameters": "object",
        "results": "object",
        "passed": "bool",
        "report_url": "string",
        "comments": "string",
        "created_by": "string",
    },
    "samples": {
        "id": "string",
        "sample_code": "string",
        "enquiry_id": "string",
        "chemical_id": "string",
        "supplier_id": "string",
        "requestor_id": "string",
        "request_date": "datetime64[ns]",
        "quantity": "float64",
        "unit": "string",
        "purpose": "string",
        "status": "string",
        "priority": "string",
        "shipping_details": "object",
        "expected_arrival_date": "datetime64[ns]",
        "actual_arrival_date": "datetime64[ns]",
        "notes": "string",
    },
    "saved_reports": {
        "id": "string",
        "user_id": "string",
        "report_name": "string",
        "report_type": "string",
        "parameters": "object",
        "created_at": "datetime64[ns]",
        "last_run": "datetime64[ns]",
        "is_scheduled": "bool",
        "schedule_frequency": "string",
        "recipients": "object",
    },
    "status_changes": {
        "id": "string",
        "enquiry_id": "string",
        "previous_state": "string",
        "new_state": "string",
        "notes": "string",
        "created_at": "datetime64[ns]",
        "created_by": "string",
        "response": "string",
        "response_date": "datetime64[ns]",
        "is_synced" : "string",
    },
    "supplier_addresses": {
        "id": "string",
        "supplier_id": "string",
        "address_type": "string",
        "address_line1": "string",
        "address_line2": "string",
        "city": "string",
        "state": "string",
        "country": "string",
        "postal_code": "string",
        "is_primary": "bool",
    },
    "supplier_chemical_pricing": {
        "id": "string",
        "supplier_chemical_id": "string",
        "price": "float64",
        "currency": "string",
        "effective_date": "datetime64[ns]",
        "expiry_date": "datetime64[ns]",
        "reason_for_change": "string",
        "created_by": "string",
    },
    "supplier_chemicals": {
        "id": "string",
        "supplier_id": "string",
        "chemical_id": "string",
        "hsn_code": "string",
        "quoted_price": "float64",
        "currency": "string",
        "purity": "string",
        "packaging": "string",
        "production_lead_time": "int64",
        "payment_terms": "string",
        "incurred_duty": "float64",
        "hazard_classification": "string",
        "plant_address": "string",
        "contact_details": "string",
        "total_production_capacity": "float64",
        "available_capacity": "float64",
        "capacity_unit": "string",
        "sample_tat": "int64",
        "remarks": "string",
        "is_active": "bool",
        "created_at": "datetime64[ns]",
        "created_by": "string",
        "updated_at": "datetime64[ns]",
    },
    "supplier_contacts": {
        "id": "string",
        "supplier_id": "string",
        "first_name": "string",
        "last_name": "string",
        "position": "string",
        "department": "string",
        "email": "string",
        "phone": "string",
        "is_primary": "bool",
    },
    "supplier_documents": {
        "id": "string",
        "supplier_id": "string",
        "document_type": "string",
        "document_name": "string",
        "file_path": "string",
        "upload_date": "datetime64[ns]",
        "uploaded_by": "string",
        "expiry_date": "datetime64[ns]",
        "status": "string",
    },
    "supplier_financial_data": {
        "id": "string",
        "supplier_id": "string",
        "fiscal_year": "string",
        "revenue": "float64",
        "profit": "float64",
        "credit_rating": "string",
        "payment_terms": "string",
        "currency": "string",
    },
    "supplier_identified_attachments": {
        "id": "string",
        "supplier_identified_id": "string",
        "file_name": "string",
        "file_path": "string",
        "content_type": "string",
        "size": "int64",
        "created_at": "datetime64[ns]",
    },
    "supplier_identified_details": {
        "id": "string",
        "enquiry_id": "string",
        "supplier_name": "string",
        "identified_at": "datetime64[ns]",
        "status": "string",
        "quotation_file_path": "string",
        "created_at": "datetime64[ns]",
        "quote_requested": "bool",
    },
    "supplier_operational_data": {
        "id": "string",
        "supplier_id": "string",
        "production_capacity": "float64",
        "capacity_unit": "string",
        "facilities_locations": "string",
        "quality_compliance": "string",
        "sustainability_rating": "string",
        "logistics_capabilities": "string",
    },
    "supplier_quotation_attachments": {
        "id": "string",
        "quote_preparation_id": "string",
        "file_name": "string",
        "file_path": "string",
        "file_type": "string",
        "content_type": "string",
        "size": "int64",
        "uploaded_by": "string",
        "created_at": "datetime64[ns]",
    },
    "suppliers": {
        "id": "string",
        "company_name": "string",
        "company_type": "string",
        "foundation_year": "int64",
        "pan_id": "string",
        "gstin": "string",
        "msme_no": "string",
        "website": "string",
        "primary_industry": "string",
        "chemistry_focus": "string",
        "quality_certifications": "string",
        "is_approved": "bool",
        "approval_date": "datetime64[ns]",
        "created_at": "datetime64[ns]",
        "created_by": "string",
        "updated_at": "datetime64[ns]",
        "supplier_id": "string",
    },
    "sample_status_history": {
        "id": "string",
        "sample_request_id": "string",
        "sample_status": "string",
        "changed_at": "datetime64[ns]",
        'enquiry_id': "string",
    },
    "users": {
        "instance_id": "string",
        "id": "string",
        "aud": "string",
        "role": "string",
        "email": "string",
        "encrypted_password": "string",
        "email_confirmed_at": "datetime64[ns]",
        "invited_at": "datetime64[ns]",
        "confirmation_token": "string",
        "confirmation_sent_at": "datetime64[ns]",
        "recovery_token": "string",
        "recovery_sent_at": "datetime64[ns]",
        "email_change_token_new": "string",
        "email_change": "string",
        "email_change_sent_at": "datetime64[ns]",
        "last_sign_in_at": "datetime64[ns]",
        "is_super_admin": "bool",
        "created_at": "datetime64[ns]",
        "updated_at": "datetime64[ns]",
        "phone": "string",
        "phone_confirmed_at": "datetime64[ns]",
        "phone_change": "string",
        "phone_change_token": "string",
        "phone_change_sent_at": "datetime64[ns]",
        "confirmed_at": "datetime64[ns]",
        "email_change_token_current": "string",
        "email_change_confirm_status": "int64",
        "banned_until": "datetime64[ns]",
        "reauthentication_token": "string",
        "reauthentication_sent_at": "datetime64[ns]",
        "is_sso_user": "bool",
        "deleted_at": "datetime64[ns]",
        "is_anonymous": "bool",
    },
}
