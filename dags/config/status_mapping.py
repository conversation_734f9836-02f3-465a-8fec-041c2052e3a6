def create_status_mapping():
    mapping = {}

    # SalesStack to ProcureStack mapping
    mapping['SalesStack_to_ProcureStack'] = {
        "enquiry_created": {
            "ProcureStack": "enquiry_received",
            "PreviousValidStates": ["enquiry_received"]
        },
        "enquiry_assigned": {
            "ProcureStack": "enquiry_assigned",
            "PreviousValidStates": ["enquiry_received", "clarification_needed"]
        },
        "po_raised": {
            "ProcureStack": "po_raised",
            "PreviousValidStates": ["enquiry_received", "enquiry_assigned","suppliers_identified",
                                    "clarification_needed","pricing_quotation_generated",
                                    "quote_requested","quote_received","quote_prepared","quote_revision_needed",
                                    "quote_approved","quote_accepted", "quote_rejected","quote_redo",
                                    "sample_request_received","sample_request_raised","sample_ready","in_transit_for_testing"
                                    ,"delivered_at_testing", "sample_passed","sample_failed","ready_for_dispatch","in_transit_to_customer"
                                    ,"sample_delivered","sample_accepted","sample_redo",
                                    "sample_rejected"]
        },
        "cancelled": {
            "ProcureStack": "cancelled",
            "PreviousValidStates": ["enquiry_received", "enquiry_assigned","suppliers_identified",
                                    "clarification_needed","pricing_quotation_generated",
                                    "quote_requested","quote_received","quote_prepared","quote_revision_needed",
                                    "quote_approved","quote_accepted", "quote_rejected","quote_redo",
                                    "sample_request_received","sample_request_raised","sample_ready","in_transit_for_testing"
                                    ,"delivered_at_testing", "sample_passed","sample_failed","ready_for_dispatch","in_transit_to_customer"
                                    ,"sample_delivered","sample_accepted","sample_redo",
                                    "sample_rejected"]
        },
        "quote_accepted": {
            "ProcureStack": "quote_accepted",
            "PreviousValidStates": ["pricing_quotation_generated","quote_redo"]
        },
        "quote_rejected": {
            "ProcureStack": "quote_rejected",
            "PreviousValidStates": ["pricing_quotation_generated"]
        },
        "quote_redo": {
            "ProcureStack": "quote_redo",
            "PreviousValidStates": ["pricing_quotation_generated"]
        },
        "quote_revision_needed": {
            "ProcureStack": "quote_revision_needed",
            "PreviousValidStates": ["pricing_quotation_generated","quote_accepted","quote_rejected","quote_redo", "po_raised"]
        }
    }

    # Create ProcureStack to SalesStack mapping
    mapping['ProcureStack_to_SalesStack'] = {
        "enquiry_assigned": {
            "SalesStack": "enquiry_assigned",
            "PreviousValidStates": ["enquiry_created"]
        },
        "suppliers_identified": {
            "SalesStack": "enquiry_assigned",
            "PreviousValidStates": ["enquiry_created","enquiry_assigned","clarification_needed"]
        },
        "clarification_needed": {
            "SalesStack": "clarification_needed",
            "PreviousValidStates": ["enquiry_created","enquiry_assigned"]
        },
        "regret": {
            "SalesStack": "regret",
            "PreviousValidStates": ["enquiry_created","enquiry_assigned","clarification_needed"]
        },
        "pricing_quotation_generated": {
            "SalesStack": "pricing_quotation_generated",
            "PreviousValidStates": ["enquiry_created","enquiry_assigned","clarification_needed","quote_redo", "quote_revision_needed"]
        }
    }

    mapping['Sample_Procure_to_Sales'] = {
        "sample_ready": {
            "SalesStack": "sample_available",
            "PreviousValidStates": ["sample_available", "sample_requested"]
        },
        "in_transit_for_testing": {
            "SalesStack": "sample_in_transit",
            "PreviousValidStates": ["sample_available", "sample_requested", "sample_ready"]
        },
        "sample_delivered": {
            "SalesStack": "sample_delivered",
            "PreviousValidStates": ["sample_available", "sample_ready", "sample_in_transit"]
        }
    }

    mapping['Sample_sales_to_procure'] = {
        "sample_requested": {
            "ProcureStack": "sample_request_received",
            "PreviousValidStates": ["pricing_quotation_generated", "quote_accepted","sample_delivered"]
        },
        "sample_accepted": {
            "ProcureStack": "sample_accepted",
            "PreviousValidStates": ["sample_delivered", "in_transit_to_customer", "ready_for_dispatch", "sample_passed"
                , "delivered_at_testing", "in_transit_for_testing", "sample_ready"]
        },
        "sample_redo": {
            "ProcureStack": "sample_redo",
            "PreviousValidStates": ["sample_delivered", "in_transit_to_customer", "ready_for_dispatch", "sample_passed"
                , "delivered_at_testing", "in_transit_for_testing", "sample_ready"]
        },
        "sample_rejected": {
            "ProcureStack": "sample_rejected",
            "PreviousValidStates": ["sample_delivered", "in_transit_to_customer", "ready_for_dispatch", "sample_passed"
                , "delivered_at_testing", "in_transit_for_testing", "sample_ready"]
        }
    }

    # LogiStack to ProcureStack mapping
    mapping['LogiStack_to_ProcureStack'] = {
        "Sample production complete": {
            "ProcureStack": "sample_ready",
            "PreviousValidStates": ["sample_request_received", "sample_request_raised","re_sample_request_raised"]
        },
        "Sample dispatch from Supplier": {
            "ProcureStack": "in_transit_for_testing",
            "PreviousValidStates": ["sample_request_received", "sample_request_raised","re_sample_request_raised", "sample_ready"]
        },
        "Origin Partner receiving the samples": {
            "ProcureStack": "delivered_at_testing",
            "PreviousValidStates": ["sample_request_received", "sample_request_raised","re_sample_request_raised", "sample_ready",
                                    "in_transit_for_testing"]
        },
        "Testing completed": {
            "ProcureStack": "sample_passed",
            "PreviousValidStates": ["sample_request_received", "sample_request_raised","re_sample_request_raised", "sample_ready",
                                    "in_transit_for_testing", "delivered_at_testing"]
        },
        "Mstack Documentation": {
            "ProcureStack": "ready_for_dispatch",
            "PreviousValidStates": ["sample_request_received", "sample_request_raised","re_sample_request_raised", "sample_ready",
                                    "in_transit_for_testing", "delivered_at_testing",
                                    "sample_passed"]
        },
        "Dispatch to Destination Partner": {
            "ProcureStack": "in_transit_to_customer",
            "PreviousValidStates": ["sample_request_received", "sample_request_raised","re_sample_request_raised", "sample_ready",
                                    "in_transit_for_testing", "delivered_at_testing",
                                    "sample_passed", "ready_for_dispatch"]
        },
        "Delivery Confirmation": {
            "ProcureStack": "sample_delivered",
            "PreviousValidStates": ["sample_request_received", "sample_request_raised","re_sample_request_raised", "sample_ready",
                                    "in_transit_for_testing", "delivered_at_testing",
                                    "sample_passed", "ready_for_dispatch", "in_transit_to_customer"]
        }
    }

    # LogiStack to ProcureStack mapping
    mapping['LogiStack_to_ProcureStack_Purchase_Order'] = {
        "PO Generation": {
            "ProcureStack": "po_created",
            "PreviousValidStates": ["created"]
        },
        "Delivery Confirmation": {
            "ProcureStack": "po_delivered",
            "PreviousValidStates": ["created"]
        },
        "Send final invoice to the customer": {
            "ProcureStack": "invoice_sent",
            "PreviousValidStates": ["created"]
        }
    }

    return mapping
