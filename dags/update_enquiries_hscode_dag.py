import logging
import sys,os
sys.path.insert(0, '/opt/airflow/dag_dependencies')
from datetime import datetime, timedelta, timezone
from typing import List, Dict, Any, Optional
import requests
import time
import os

from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.models import Variable

from config.db_config import DatabaseConfig
from config.default import DEFAULT_DAG_ARGS
from db.postgres_operations import PostgresOperations
from failure_task import send_slack_alert
from exception_task_email_dag import send_exception_slack_alert

# Constants
BATCH_SIZE = 50
MAX_RETRIES = 3
RETRY_DELAY = 5  # seconds
TABLE_NAME = 'public.enquiries'
API_ENDPOINT = 'v1/code-resolver/resolve-enhanced'

class EnquiryHSCodeUpdater:
    """Class to handle HS code updates for enquiries"""

    def __init__(self):
        self.db: Optional[PostgresOperations] = None
        base_url = Variable.get('pricing_engine_endpoint', default_var=os.getenv('PRICING_ENGINE_ENDPOINT'))
        self.api_url = f"{base_url}/{API_ENDPOINT}"

    def connect_to_db(self):
        """Connect to the database"""
        try:
            db_params = DatabaseConfig.get_procuro_stack_postgres_params()
            self.db = PostgresOperations(db_params)
            logging.info("Connected to database")
        except Exception as e:
            logging.error(f"Error connecting to database: {str(e)}")
            raise

    def fetch_enquiries_without_hs_codes(self) -> List[Dict[str, Any]]:
        """Fetch enquiries that need HS codes and are in enquiry_received state"""
        try:
            query = f"""
                SELECT id, chemical_name, category
                FROM {TABLE_NAME}
                WHERE current_status IN ('enquiry_received', 'enquiry_assigned', 'clarification_needed')
                AND llm_suggested_hs_codes IS NULL
                ORDER BY created_at
            """
            results = self.db.read_data(query)
            logging.info(f"Found {len(results)} enquiries that need HS codes")
            return results
        except Exception as e:
            logging.error(f"Error fetching enquiries: {str(e)}")
            raise

    def fetch_hs_code(self, chemical_name: str, category: str = None) -> Optional[Dict[str, List[str]]]:
        """Fetch the HS codes and product families for a given chemical name using the code resolver API."""
        try:
            params = {
                "chemical_name": chemical_name
            }
            if category:
                if category == "Agro":
                    category = "Agro Chemicals"
                elif category == "CaseIndia":
                    category = "Coatings"
                params["chemical_application"] = category
            
            # No timeout - wait for response
            response = requests.get(self.api_url, params=params)
            response.raise_for_status()
            data = response.json()
            
            result = {}
            hs_code = data.get("hs_code")
            if hs_code:
                result["hs_codes"] = [hs_code]
                logging.info(f"Found HS code {hs_code} for chemical {chemical_name}")
            else:
                result["hs_codes"] = []
                logging.warning(f"No HS code found for chemical: {chemical_name}")

            product_family = data.get("product_family")
            if product_family:
                result["product_families"] = [product_family]
                logging.info(f"Found product family {product_family} for chemical {chemical_name}")
            else:
                result["product_families"] = []
                logging.warning(f"No product family found for chemical: {chemical_name}")
            
            return result
            
        except requests.exceptions.RequestException as e:
            logging.error(f"API request failed for chemical '{chemical_name}': {str(e)}")
            return None
        except Exception as e:
            logging.error(f"Error fetching HS code for chemical '{chemical_name}': {str(e)}")
            return None

    def update_chemicals_table(self, chemical_name: str, hs_codes: List[str], product_families: List[str]) -> bool:
        """Update the chemicals table with HS codes and product families if a match is found"""
        try:
            # First check if chemical exists
            check_query = """
                SELECT id FROM chemicals 
                WHERE LOWER(chemical_name) = LOWER(:chemical_name)
            """
            result = self.db.read_data(check_query, {"chemical_name": chemical_name})
            
            if not result:
                logging.info(f"No matching chemical found in chemicals table for: {chemical_name}")
                return False

            # Update the chemicals table
            update_query = """
                UPDATE chemicals 
                SET user_selected_hs_code = :hs_code,
                    user_selected_product_family = :product_family
                WHERE LOWER(chemical_name) = LOWER(:chemical_name)
            """
            
            # Use the first HS code and product family if available
            params = {
                "chemical_name": chemical_name,
                "hs_code": hs_codes[0] if hs_codes else None,
                "product_family": product_families[0] if product_families else None
            }
            
            self.db.write_data(update_query, params)
            logging.info(f"Updated chemicals table for chemical: {chemical_name}")
            return True

        except Exception as e:
            logging.error(f"Error updating chemicals table for chemical '{chemical_name}': {str(e)}")
            return False

    def get_chemical_details(self, chemical_name: str) -> Optional[Dict[str, str]]:
        """Get HS code and product family from chemicals table if available"""
        try:
            query = """
                SELECT hs_code as hs_code, product_family as product_family 
                FROM chemicals 
                WHERE LOWER(chemical_name) = LOWER(:chemical_name)
                AND (hs_code IS NOT NULL OR product_family IS NOT NULL)
            """
            result = self.db.read_data(query, {"chemical_name": chemical_name})
            
            if result and len(result) > 0:
                logging.info(f"Found existing values in chemicals table for: {chemical_name}")
                return result[0]
            return None

        except Exception as e:
            logging.error(f"Error checking chemicals table for chemical '{chemical_name}': {str(e)}")
            return None

    def update_enquiry_hs_codes(self, enquiry: Dict[str, Any]) -> bool:
        """Update HS codes for a single enquiry"""
        try:
            chemical_name = enquiry.get('chemical_name')
            category = enquiry.get('category')
            
            if not chemical_name:
                logging.warning(f"No chemical name found for enquiry {enquiry.get('id')}")
                return False

            # Always get suggested fields from API
            api_result = self.fetch_hs_code(chemical_name, category)
            if not api_result:
                return False

            # Format HS codes and product families as PostgreSQL array literals for suggested fields
            hs_codes = "{" + ",".join(f'"{code}"' for code in api_result["hs_codes"]) + "}"
            product_families = "{" + ",".join(f'"{family}"' for family in api_result["product_families"]) + "}"

            # Check chemicals table for selected fields
            chemical_details = self.get_chemical_details(chemical_name)
            
            # Build update query
            update_fields = [
                "llm_suggested_hs_codes = :hs_codes",
                "llm_suggested_product_families = :product_families"
            ]
            params = {
                "id": enquiry["id"],
                "hs_codes": hs_codes,
                "product_families": product_families
            }

            # Add selected fields if available in chemicals table
            if chemical_details:
                if chemical_details.get("hs_code"):
                    update_fields.append("user_selected_hs_code = :user_selected_hs_code")
                    params["user_selected_hs_code"] = chemical_details["hs_code"]
                
                if chemical_details.get("product_family"):
                    update_fields.append("user_selected_product_family = :user_selected_product_family")
                    params["user_selected_product_family"] = chemical_details["product_family"]

            update_query = f"""
                UPDATE {TABLE_NAME}
                SET {', '.join(update_fields)}
                WHERE id = :id
            """
            
            self.db.write_data(update_query, params)
            logging.info(f"Updated enquiry {enquiry['id']} with API suggestions and chemicals table selections")
            return True

        except Exception as e:
            logging.error(f"Error updating HS codes for enquiry {enquiry.get('id')}: {str(e)}")
            return False

    def process_batch(self, enquiries: List[Dict[str, Any]]) -> int:
        """Process a batch of enquiries"""
        success_count = 0
        for enquiry in enquiries:
            if self.update_enquiry_hs_codes(enquiry):
                success_count += 1
        return success_count

    def update_hs_codes(self) -> None:
        """Main function to update HS codes for all eligible enquiries"""
        try:
            self.connect_to_db()
            enquiries = self.fetch_enquiries_without_hs_codes()
            
            if not enquiries:
                logging.info("No enquiries found that need HS codes")
                return

            total_count = len(enquiries)
            success_count = 0

            # Process in batches
            for i in range(0, total_count, BATCH_SIZE):
                batch = enquiries[i:i + BATCH_SIZE]
                success_count += self.process_batch(batch)
                logging.info(f"Processed batch of {len(batch)} enquiries")

            logging.info(f"HS code update completed: {success_count}/{total_count} enquiries updated successfully")

        except Exception as e:
            logging.error(f"Error in update_hs_codes: {str(e)}")
            raise
        finally:
            if self.db:
                self.db.close()

def update_hs_codes_task() -> None:
    """Task function to update HS codes"""
    updater = EnquiryHSCodeUpdater()
    updater.update_hs_codes()

# Create the DAG
dag = DAG(
    'update_enquiry_hs_codes',
    default_args={
        **DEFAULT_DAG_ARGS,
        'on_failure_callback':send_slack_alert 
    },
    description='Update HS codes for enquiries in specific statuses',
    schedule=timedelta(minutes=3),
    catchup=False,
    start_date=datetime(2024, 1, 1, tzinfo=timezone.utc),
    tags=['database', 'update', 'hs_codes'],
    dagrun_timeout=timedelta(minutes=30),  # Increased from 10 to 30 minutes
    max_active_runs=1
)

# Create the update task
update_task = PythonOperator(
    task_id='update_hs_codes',
    python_callable=update_hs_codes_task,
    dag=dag,
    retries=0,  # Added retry
    retry_delay=timedelta(minutes=5),
    execution_timeout=timedelta(minutes=25)  # Increased from 5 to 25 minutes
) 