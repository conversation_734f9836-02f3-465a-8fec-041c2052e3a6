import logging
import sys,os
sys.path.insert(0, '/opt/airflow/dag_dependencies')
import requests
from contextlib import contextmanager
from datetime import datetime, timedelta, timezone
from typing import Dict, Any, Optional

from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.models.baseoperator import chain
from airflow.models import Variable


from config.db_config import DatabaseConfig
from config.default import DEFAULT_DAG_ARGS, QueryRun  # Changed from dags.config.default
from config.status_mapping import create_status_mapping
from config.supabase_config import SupabaseConfig
from db.supabase_operations import SupabaseOperations
from db.postgres_operations import PostgresOperations
from db.mongo_operations import MongoOperations
from ses_hook import SESHook
from failure_task import send_slack_alert
from exception_task_email_dag import send_exception_slack_alert

# Constants
BATCH_SIZE = 100
TABLE_NAME = 'public.enquiries'
STATUS_HISTORY_TABLE = 'public.enquiry_status_history'
STATUS_CHANGES_TABLE = 'public.status_changes'
# LogiStack API constants
ORDER_BOOK_URL = Variable.get("order_book_url")
CUSTOMER_ORDER_URL = Variable.get("customer_order_url")
SUPPLIER_ORDER_BOOK_URL = Variable.get("supplier_order_book_url")
MAX_RETRIES = 0
RETRY_DELAY = timedelta(minutes=5)

class SalesProcureStatusSyncManager:
    """Manager class for syncing status from Sales to Procure"""

    def __init__(self):
        self.sales_db: Optional[PostgresOperations] = None
        self.procure_db: Optional[PostgresOperations] = None
        self.procure_supabase: Optional[SupabaseOperations] = None
        self.sales_supabase: Optional[SupabaseOperations] = None
        self.logi_db: Optional[MongoOperations] = None

        # Initialize status mapping by fetching from config
        # Use the correct mapping key for SalesStack
        self.status_mapping = create_status_mapping()['SalesStack_to_ProcureStack']
        self.valid_status_names = list(self.status_mapping.keys())

        self.procure_po_documents_bucket = SupabaseConfig.STORAGE_BUCKETS['PROCURE_STACK']['PURCHASE_ORDER_DOCUMENTS']
        self.sales_po_documents_bucket = SupabaseConfig.STORAGE_BUCKETS['SALES_STACK']['PURCHASE_ORDER_DOCUMENTS']
        self.procure_quotation_documents_bucket = SupabaseConfig.STORAGE_BUCKETS['PROCURE_STACK']['QUOTATION_DOCUMENTS']
        self.sales_quotation_documents_bucket = SupabaseConfig.STORAGE_BUCKETS['SALES_STACK']['QUOTATION_DOCUMENTS']
        logging.warning(f"Fetching from {QueryRun}")

        
    @contextmanager
    def database_connections(self, procure_bucket_name=None, sales_bucket_name=None):
        """Context manager for database connections
        
        Args:
            procure_bucket_name: Optional bucket name for procure supabase
            sales_bucket_name: Optional bucket name for sales supabase
        """
        try:
            sales_params = DatabaseConfig.get_sales_stack_postgres_params()
            procure_params = DatabaseConfig.get_procuro_stack_postgres_params()
            logi_params = DatabaseConfig.get_logi_stack_mongo_params()

            self.sales_db = PostgresOperations(sales_params)
            self.procure_db = PostgresOperations(procure_params)
            self.logi_db = MongoOperations(*logi_params)

            # Initialize Supabase connections
            procure_supabase_params = SupabaseConfig.get_procure_stack_supabase_params()
            sales_supabase_params = SupabaseConfig.get_sales_stack_supabase_params()

            # Pass bucket names to SupabaseOperations
            procure_supabase_params['bucket_name'] = procure_bucket_name or self.procure_po_documents_bucket
            sales_supabase_params['bucket_name'] = sales_bucket_name or self.sales_po_documents_bucket

            self.procure_supabase = SupabaseOperations(procure_supabase_params)
            self.sales_supabase = SupabaseOperations(sales_supabase_params)
            logging.info(f"Connected to databases - Sales: {sales_params['host']}, Procure: {procure_params['host']}")
            yield

        finally:
            if self.sales_db:
                self.sales_db.close()
            if self.procure_db:
                self.procure_db.close()
            if self.logi_db:
                self.logi_db.close()
            logging.info("Database connections closed")

    def sync_table_data(self, select_query: str, select_query_params: Dict[str, Any], insert_or_update_query: str,
                        use_case: str, **kwargs) -> Optional[bool]:
        """Sync table data between Sales and Procure stacks"""
        try:
            status_changes = self.sales_db.read_data(select_query, select_query_params)

            if not status_changes:
                logging.info(f"No records found to sync for {use_case}")
                return True

            logging.info(f"{use_case}: {len(status_changes)} records found to sync")

            success_count = 0

            for record in status_changes:
                try:
                    # Convert RowMapping to dictionary
                    record_dict = dict(record)
                    logging.info(f"Processing record: {record_dict}")

                    if use_case == 'main_enquiry_status':
                        logging.info(f"Handling use case: {use_case} for record ID {record_dict.get('id')}")
                        current_status = record_dict.get('current_status')
                        mapping_entry = self.status_mapping.get(current_status)
                        logging.info(f"Status mapping entry for '{current_status}': {mapping_entry}")

                        if not mapping_entry:
                            logging.warning(f"No mapping found for status '{current_status}' in status_mapping. Skipping record {record_dict.get('id')}")
                            continue

                        procure_status = mapping_entry.get('ProcureStack')
                        previous_valid_states = mapping_entry.get('PreviousValidStates')
                        logging.info(f"Derived ProcureStack status: '{procure_status}', Previous valid states: {previous_valid_states}")

                        if not procure_status:
                            logging.warning(f"No 'ProcureStack' status defined for Sales status '{current_status}' in mapping. Skipping record {record_dict.get('id')}")
                            continue

                        if current_status == 'enquiry_assigned' and self.validate_clarification_data(record_dict['id']):
                            logging.info(f"Skipping record {record_dict.get('id')} as it has pending clarification responses")
                            continue

                        # Set the target Procure status
                        record_dict['current_status'] = procure_status
                        # Pass the valid previous states for the UPDATE query's WHERE clause
                        # Pass an empty list if None to simplify SQL query handling
                        record_dict['previous_valid_states'] = previous_valid_states if previous_valid_states is not None else []
                        logging.info(f"Updated record_dict before DB write: {record_dict}")
                        # Validation is now handled in the SQL UPDATE query itself

                    elif use_case == 'enquiry_status':
                        mapping_entry = self.status_mapping.get(record_dict['new_state'])
                        if not mapping_entry:
                            logging.warning(f"No mapping found for status '{record_dict['new_state']}' in status_mapping. Skipping record {record_dict['id']}")
                            continue
                        # Extract the ProcureStack status from the nested dictionary
                        record_dict['new_state'] = mapping_entry['ProcureStack']

                    elif (use_case == 'purchase_order_document' or
                          use_case == 'Quote_Feedback_Document' or
                          use_case == 'Sample_Feedback_Document' or
                          use_case == 'purchase_order_items_attachments'):
                        # Normalize the file path
                        file_path = record_dict['file_path'].strip()

                        # Check if file exists in sales bucket
                        if not self.procure_supabase.check_file_exists(file_path):
                            # Transfer file between buckets if it doesn't exist
                            if not self.sales_supabase.transfer_file(file_path, self.procure_supabase):
                                continue

                    # Use the modified dictionary for database write
                    self.procure_db.write_data(insert_or_update_query, record_dict)
                    success_count += 1

                except Exception as e:
                    logging.error(f"Error syncing status change {record.get('id', 'unknown')}: {str(e)}")
                    continue

            logging.info(f"Successfully synced {success_count}/{len(status_changes)} records for {use_case}")
            return True

        except Exception as e:
            logging.error(f"Error in sync_table_data for {use_case} and Query: {select_query} and Params:{select_query_params} : {str(e)}")
            send_exception_slack_alert(e, kwargs)
            return False

    def validate_clarification_data(self, enquiry_id, **kwargs) -> bool:
        """Sync clarification responses from sales to procure database"""
        try:
            # Get all clarification IDs from procure database
            procure_query = """
                SELECT id
                FROM public.enquiry_clarifications
                WHERE status = 'pending' and enquiry_id = :enquiry_id
            """
            procure_clarifications = self.procure_db.read_data(procure_query, {"enquiry_id": enquiry_id})

            if procure_clarifications:
                logging.info("Pending clarification records found in procure database")
                return False

            return True
        except Exception as e:
            logging.error(f"Error syncing clarification responses: {str(e)}")
            send_exception_slack_alert(e, context_info=kwargs)
            return False


    def update_sales_enquiry_status(self, **kwargs) -> bool:
        """Update enquiry status in sales database"""
        try:

            current_status_query = f"""
                SELECT id, current_status, last_status_change
                FROM enquiries
                WHERE last_status_change >= :execution_time
            """

            update_query = f"""
                                UPDATE enquiries
                                SET current_status = :current_status,
                                    last_status_change = :last_status_change
                                WHERE id = :id 
                                AND current_status != :current_status
                                AND (
                                    array_length(:previous_valid_states, 1) IS NULL
                                    OR current_status::text = ANY(:previous_valid_states)
                                )
                        """

            self.sync_table_data(current_status_query, {"execution_time": QueryRun}, update_query, 'main_enquiry_status')

            return True

        except Exception as e:
            logging.error(f"Error updating enquiry status in sales DB: {str(e)}")
            send_exception_slack_alert(e, kwargs)
            return False

    def sync_status_history_tables(self, **kwargs) -> bool:
        """Sync status changes to enquiry_status_history"""
        try:
            # Get data from procure status_changes table
            select_query = """
                SELECT 
                    id, 
                    enquiry_id, 
                    status as previous_state,
                    status as new_state, 
                    notes, 
                    created_at, 
                    sales_agent_email as created_by, 
                    '' as response,
                    CURRENT_TIMESTAMP as response_date 
                FROM enquiry_status_history stc
                WHERE stc.created_at >= :execution_time 
                AND stc.status::text = ANY(:valid_statuses)
                ORDER BY stc.created_at
            """

            insert_query = """
                INSERT INTO status_changes (
                    id, 
                    enquiry_id, 
                    previous_state, 
                    new_state, 
                    notes, 
                    created_at, 
                    created_by, 
                    response, 
                    response_date
                )
                VALUES (
                    :id, :enquiry_id, :previous_state, :new_state, :notes, 
                    :created_at, :created_by, :response, :response_date
                )
                ON CONFLICT (id) DO NOTHING
            """

            self.sync_table_data(
                select_query, 
                {
                    "execution_time": QueryRun,
                    "valid_statuses": self.valid_status_names
                },
                insert_query,
                'enquiry_status'
            )

            return True

        except Exception as e:
            logging.error(f"Error syncing status history: {str(e)}")
            send_exception_slack_alert(e, kwargs)
            return False

    def sync_clarification_responses(self, **kwargs) -> bool:
        """Sync clarification responses from sales to procure database"""
        try:
            # Get all clarification IDs from procure database
            procure_query = """
                SELECT id
                FROM public.enquiry_clarifications
                WHERE status = 'pending'
            """
            procure_clarifications = self.procure_db.read_data(procure_query)

            if not procure_clarifications:
                logging.info("No pending clarification records found in procure database")
                return True

            clarification_ids = [record["id"] for record in procure_clarifications]
            logging.info(f"Found {len(clarification_ids)} pending clarification records in procure database")

            # Get resolved/rejected clarifications from sales database
            sales_query = """
                SELECT 
                    id,
                    response,
                    resolved_at,
                    status
                FROM public.enquiry_clarifications
                WHERE id = ANY(:clarification_ids)
                AND status IN ('resolved', 'rejected')
            """

            resolved_clarifications = self.sales_db.read_data(sales_query, {
                "clarification_ids": clarification_ids
            })

            if not resolved_clarifications:
                logging.info("No resolved/rejected clarification records found in sales database")
                return True

            logging.info(
                f"Found {len(resolved_clarifications)} resolved/rejected clarification records in sales database")

            # Update procure database with resolved clarifications
            update_query = """
                UPDATE public.enquiry_clarifications
                SET 
                    response = :response,
                    resolved_at = :resolved_at,
                    status = :status
                WHERE id = :id
            """

            success_count = 0
            for record in resolved_clarifications:
                try:
                    self.procure_db.write_data(update_query, {
                        "id": record["id"],
                        "response": record["response"],
                        "resolved_at": record["resolved_at"],
                        "status": record["status"]
                    })
                    success_count += 1
                    logging.info(f"Updated clarification record {record['id']} in procure database")
                except Exception as e:
                    logging.error(f"Error updating clarification record {record['id']}: {str(e)}")

            logging.info(f"Successfully updated {success_count}/{len(resolved_clarifications)} clarification records")
            return True

        except Exception as e:
            logging.error(f"Error syncing clarification responses: {str(e)}")
            send_exception_slack_alert(e, kwargs)
            return False

    def sync_purchase_order(self, **kwargs) -> bool:
        try:
            select_po_query = """
                select po.id, po.enquiry_id, po.po_number, po.notes, po.created_by, c.customer_full_name, c.customer_email, c.customer_phone,
                       c.customer_company, po.history_status_id as status_change_id, po.frequency, po.created_at
                from purchase_orders po inner join customer c on c.id = po.customer_id
                where po.created_at >= :execution_time
            """

            insert_po_query = """insert into purchase_orders_raised (id, enquiry_id, po_number, notes, created_by, 
            customer_full_name, customer_email, customer_phone, customer_company, status_change_id, frequency, created_at)
                                values (:id, :enquiry_id, :po_number, :notes, :created_by, :customer_full_name, 
                                :customer_email, :customer_phone, :customer_company, :status_change_id, :frequency, :created_at)
                                on conflict (id) do nothing
                        """
            self.sync_table_data(select_po_query, {"execution_time": QueryRun}, insert_po_query,
                                 'purchase_order')

            select_document_query="""select id, purchase_order_id, file_name, file_path, content_type, size, created_at, doc_type 
                                    from purchase_order_attachments
                                    where created_at >= :execution_time"""

            insert_document_query ="""insert into purchase_order_attachments (id, purchase_order_id, file_name, file_path, content_type, size, created_at, doc_type)
                                values (:id, :purchase_order_id, :file_name, :file_path, :content_type, :size, :created_at, :doc_type)
                                on conflict (id) do nothing
            """

            self.sync_table_data(select_document_query, {"execution_time": QueryRun}, insert_document_query,
                                 'purchase_order_document')
            return True
        except Exception as e:
            logging.error(f"Error syncing purchase order: {str(e)}")
            send_exception_slack_alert(e, kwargs)
            return False
    


    def sync_purchase_order_items(self, **kwargs) -> bool:
        try:
            # Sync purchase order items
            select_items_query = """
                select id, purchase_order_id, enquiry_id,chemical_name, po_value, po_currency, remarks,
                    frequency, created_at, updated_at
                from purchase_order_items
                where created_at >= :execution_time
            """

            insert_items_query = """insert into purchase_order_items (id, purchase_order_id, enquiry_id, chemical_name, po_value, po_currency, remarks, 
                frequency, created_at, updated_at)
                values (:id, :purchase_order_id, :enquiry_id, :chemical_name, :po_value, :po_currency, :remarks, 
                :frequency, :created_at, :updated_at)
                on conflict (id) do nothing
            """

            self.sync_table_data(select_items_query, {"execution_time": QueryRun}, insert_items_query,
                                'purchase_order_items')

            # Sync purchase order item attachments
            select_attachments_query = """
                select id, created_at, purchase_order_id, purchase_order_items_id, file_name, 
                    file_path, content_type, doc_type, size
                from purchase_order_items_attachments
                where created_at >= :execution_time
            """

            insert_attachments_query = """insert into purchase_orders_items_attachments (id, created_at, purchase_order_id, 
                purchase_orders_items_id, file_name, file_path, content_type, doc_type, size)
                values (:id, :created_at, :purchase_order_id, :purchase_order_items_id, 
                :file_name, :file_path, :content_type, :doc_type, :size)
                on conflict (id) do nothing
            """

            self.sync_table_data(select_attachments_query, {"execution_time": QueryRun}, insert_attachments_query,
                                'purchase_order_items_attachments')

            return True
        except Exception as e:
            logging.error(f"Error syncing purchase order items: {str(e)}")
            send_exception_slack_alert(e, kwargs)
            return False

    def sync_quote_feedback_details(self, **kwargs) -> bool:
        """Sync quote generation details from sales to procure database"""
        try:
            # Get quote generation details from sales database
            select_query = """
                    select qf.id, qf.enquiry_id, qf.response, qf.reason, qf.submitted_by, qf.created_at, esh.id as status_change_id, qf.remarks from quotation_feedback qf
                    inner join enquiry_status_history esh on qf.enquiry_id = esh.enquiry_id and qf.response = esh.status::text
                    where esh.status in ('quote_accepted' , 'quote_redo', 'quote_rejected' ) and qf.created_at >= :execution_time """

            insert_query = """
            insert into quotation_feedback (id, enquiry_id, response, reason, submitted_by, created_at, status_change_id, remarks)
            values (:id, :enquiry_id, :response, :reason, :submitted_by, :created_at, :status_change_id, :remarks)
            on conflict (id) do nothing
            """


            self.sync_table_data(select_query, {"execution_time": QueryRun}, insert_query,
                             'Quote_Feedback')

            select_document_query ="""select id, feedback_id, file_name, file_path, content_type, size, created_at from
                                        quotation_feedback_attachments where created_at >= :execution_time """

            insert_document_query ="""insert into quotation_feedback_attachments (id, feedback_id, file_name, file_path, content_type, size, created_at)
                            values (:id, :feedback_id, :file_name, :file_path, :content_type, :size, :created_at)
                            on conflict (id) do nothing
        """
            self.sync_table_data(select_document_query, {"execution_time": QueryRun}, insert_document_query,
                                 'Quote_Feedback_Document')
            return True


        except Exception as e:
            logging.error(f"Error syncing quote generation details: {str(e)}")
            send_exception_slack_alert(e, kwargs)
            return False

    def sync_sample_feedback_details(self, **kwargs) -> bool:
        """Sync quote generation details from sales to procure database"""
        try:
            # Get quote sample details from sales database
            select_query = """
                    select sf.id, sf.enquiry_id, sf.response, sf.reason, sf.submitted_by, sf.created_at, esh.id as status_change_id, sf.remarks  from sample_feedback sf
                    inner join enquiry_status_history esh on esh.enquiry_id = sf.enquiry_id and esh.status:: text = sf.response
                    where sf.created_at >= :execution_time  and sf.response in ('sample_accepted', 'sample_redo', 'sample_rejected')"""

            insert_query = """
            insert into sample_feedback (id, enquiry_id, response, reason, submitted_by, created_at, status_change_id, remarks)
            values (:id, :enquiry_id, :response, :reason, :submitted_by, :created_at, :status_change_id, :remarks)
            on conflict (id) do nothing
            """


            self.sync_table_data(select_query, {"execution_time": QueryRun}, insert_query,
                             'sample_feedback_details')

            select_document_query ="""select id, feedback_id, file_name, file_path, content_type, size, created_at 
                                    from sample_feedback_attachments where created_at >= :execution_time """

            insert_document_query ="""insert into sample_feedback_attachments (id, feedback_id, file_name, file_path, content_type, size, created_at)
                            values (:id, :feedback_id, :file_name, :file_path, :content_type, :size, :created_at)
                            on conflict (id) do nothing
        """
            self.sync_table_data(select_document_query, {"execution_time": QueryRun}, insert_document_query,
                                 'sample_feedback_document')




            return True


        except Exception as e:
            logging.error(f"Error syncing quote generation details: {str(e)}")
            send_exception_slack_alert(e, kwargs)
            return False

    def sync_sample_requested_tables(self, **kwargs) -> bool:
        """Sync status changes to enquiry_status_history"""
        try:
            # Get data from procure status_changes table
            select_query = """
               select id, enquiry_id, quantity, quantity_unit, delivery_address, delivery_city,
               delivery_country, delivery_postal_code, remarks, created_at, created_by,
               'sample_request_received' as status, sales_team_member, status_history_id, contact_phone, 
               contact_email, sample_poc from sample_requests
               where created_at >= :execution_time and status = 'sample_requested'
            """

            insert_query = """
                insert into sample_requests(id, enquiry_id, quantity, quantity_unit, delivery_address, delivery_city,
                delivery_country, delivery_postal_code, remarks, created_at, created_by,
                status, sales_team_member, status_history_id, contact_phone, contact_email, sample_poc)
                values (:id, :enquiry_id, :quantity, :quantity_unit, :delivery_address, :delivery_city,
                :delivery_country, :delivery_postal_code, :remarks, :created_at, :created_by,
                :status, :sales_team_member, :status_history_id, :contact_phone, :contact_email, :sample_poc)
                on conflict (id) do nothing
            """

            self.sync_table_data(
                select_query,
                {"execution_time": QueryRun},
                insert_query,
                'sample_requested_status'
            )

            select_document_query = """select id, sample_request_id, file_name, file_path, content_type, size, created_at from
                                                   public.sample_requests_attachments where created_at >= :execution_time """

            insert_document_query = """insert into sample_requests_attachments (id, sample_request_id, file_name, file_path, content_type, size, created_at)
                                       values (:id, :sample_request_id, :file_name, :file_path, :content_type, :size, :created_at)
                                       on conflict (id) do nothing
                   """
            self.sync_table_data(select_document_query, {"execution_time": QueryRun}, insert_document_query,
                                 'Sample_Feedback_Document')

            select_query = """
                           select id, sample_request_id, 
                                  case when sample_status = 'sample_requested' 
                                           then 'sample_request_received' 
                                       else sample_status end as sample_status, changed_at, enquiry_id 
                           from sample_status_history
                           where changed_at >= :execution_time 
                               and sample_status in ('sample_requested','sample_redo','sample_accepted','sample_rejected')
                           """

            insert_query = """
                           INSERT INTO sample_status_history (id, sample_request_id, sample_status, changed_at, enquiry_id)
                           VALUES (:id, :sample_request_id, :sample_status, :changed_at, :enquiry_id) 
                           ON CONFLICT (id) DO NOTHING
                           """

            self.sync_table_data(select_query, {"execution_time": QueryRun},
                                 insert_query,
                                 'sample_status_history')


            select_query = """
                           select id, status, last_status_change from sample_requests 
                           where last_status_change >= :execution_time 
                                 and status in ('sample_redo','sample_accepted','sample_rejected')
                           """

            update_query = """
                           update sample_requests set status = :status, last_status_change = :last_status_change where id = :id
                           """

            self.sync_table_data(select_query, {"execution_time": QueryRun},
                                 update_query,
                                 'sample_requested_status')

            return True

        except Exception as e:
            logging.error(f"Error syncing status history: {str(e)}")
            send_exception_slack_alert(e, kwargs)
            return False

    def get_request_headers(self, employee_id: str) -> Dict[str, str]:
        """Returns headers with entityId and entityType for LogiStack API authentication."""
        if not employee_id:
            raise ValueError("employee_id is required to make API requests")
        return {
            'entityId': employee_id,
            'entityType': 'employee',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }

    def fetch_employee_id_by_email(self, email: str) -> Optional[str]:
        """Fetches employee _id from LogiStack MongoDB by email."""
        if not email:
            logging.warning("[Mongo] No email provided to fetch employee ID.")
            return None
        try:
            employee = self.logi_db.read_data(
                collection_name='employee',
                query={"email": email, "deleted": False}
            )
            if employee:
                employee_id = str(employee[0]['_id'])
                logging.info(f"[Mongo] Found employee_id {employee_id} for email {email}")
                return employee_id
            else:
                logging.warning(f"[Mongo] No employee found for email: {email}")
                return None
        except Exception as e:
            raise Exception (f"[Mongo][Error] Failed to fetch employee for email {email}: {e}")

    def _delete_logistack_order_book(self, sample_id: str, employee_id: str, **kwargs) -> bool:
        """Soft delete order book entry from LogiStack when sample is cancelled"""
        try:
            # First, find the order book document in MongoDB using sample_id as purchaseOrderNumber
            order_book_doc = self.logi_db.read_data(
                collection_name='orderBook',
                query={
                    "purchaseOrderNumber": sample_id,
                    "deleted": False,
                    "orderType": "SAMPLE"
                }
            )
            
            if not order_book_doc:
                raise Exception (f"No order book document found in LogiStack for sample {sample_id}")
            
            # Get the MongoDB _id which is the orderBookId for the API
            order_book_id = str(order_book_doc[0]['_id'])
            logging.info(f"Found order book ID {order_book_id} for sample {sample_id}")
            
            # Step 1: Delete Customer Orders first
            if not self._delete_customer_orders(sample_id, employee_id):
                raise Exception (f"Failed to delete customer orders for sample {sample_id}")
            
            # Step 2: Delete Supplier Orders second
            if not self._delete_supplier_orders(sample_id, employee_id):
                raise Exception (f"Failed to delete supplier orders for sample {sample_id}")
            
            # Step 3: Now delete the Order Book
            delete_url = f"{ORDER_BOOK_URL}/{order_book_id}"
            headers = self.get_request_headers(employee_id)
            logging.info(f"Calling LogiStack API to delete order book entry: {delete_url}")
            
            response = requests.delete(delete_url, headers=headers, timeout=30, verify=False)
            
            if response.status_code == 204:  # NO_CONTENT
                logging.info(f"Successfully soft deleted order book entry {order_book_id} for sample {sample_id} from LogiStack")
                return True
            else:
                logging.warning(f"LogiStack API returned status {response.status_code} for order book {order_book_id}. Response: {response.text}")
                return False
                
        except Exception as e:
            raise Exception (f"Error calling LogiStack API to delete order book for sample {sample_id}: {str(e)}")

    def _delete_customer_orders(self, sample_id: str, employee_id: str, **kwargs) -> bool:
        """Delete customer orders for a given sample_id (purchaseOrderNumber)"""
        try:
            # Get customer orders by PO number
            customer_orders = self.logi_db.read_data(
                collection_name='customerOrder',
                query={
                    "purchaseOrderNumber": sample_id,
                    "deleted": False
                }
            )
            
            if not customer_orders:
                logging.info(f"No customer orders found for sample {sample_id}")
                return True
            
            logging.info(f"Found {len(customer_orders)} customer orders to delete for sample {sample_id}")
            
            # Delete each customer order
            for customer_order in customer_orders:
                customer_order_id = str(customer_order['_id'])
                delete_url = f"{CUSTOMER_ORDER_URL}/{customer_order_id}"
                headers = self.get_request_headers(employee_id)
                
                logging.info(f"Deleting customer order {customer_order_id} for sample {sample_id}")
                response = requests.delete(delete_url, headers=headers, timeout=30, verify=False)
                
                if response.status_code == 204:
                    logging.info(f"Successfully deleted customer order {customer_order_id}")
                else:
                    raise Exception(f"Failed to delete customer order {customer_order_id}. Status: {response.status_code}, Response: {response.text}")
            
            return True
            
        except Exception as e:
            raise Exception(f"Error deleting customer orders for sample {sample_id}: {str(e)}")

    def _delete_supplier_orders(self, sample_id: str, employee_id: str, **kwargs) -> bool:
        """Delete supplier orders for a given sample_id (purchaseOrderNumber)"""
        try:
            # Get supplier orders by PO number
            supplier_orders = self.logi_db.read_data(
                collection_name='supplierOrderBook',
                query={
                    "purchaseOrderNumber": sample_id,
                    "deleted": False
                }
            )
            
            if not supplier_orders:
                logging.info(f"No supplier orders found for sample {sample_id}")
                return True
            
            logging.info(f"Found {len(supplier_orders)} supplier orders to delete for sample {sample_id}")
            
            # Delete each supplier order
            for supplier_order in supplier_orders:
                supplier_order_id = str(supplier_order['_id'])
                delete_url = f"{SUPPLIER_ORDER_BOOK_URL}/{supplier_order_id}"
                headers = self.get_request_headers(employee_id)
                
                logging.info(f"Deleting supplier order {supplier_order_id} for sample {sample_id}")
                response = requests.delete(delete_url, headers=headers, timeout=30, verify=False)
                
                if response.status_code == 204:
                    logging.info(f"Successfully deleted supplier order {supplier_order_id}")
                else:
                    raise Exception(f"Failed to delete supplier order {supplier_order_id}. Status: {response.status_code}, Response: {response.text}")
            
            return True
            
        except Exception as e:
            raise Exception(f"Error deleting supplier orders for sample {sample_id}: {str(e)}")

    def sync_sample_cancellation(self, **kwargs) -> bool:
        """Auto-update sample state to sample_cancelled for all samples of an enquiry when enquiry is moved to Cancelled 
        and current state is not Sample Ready or above"""
        try:
            # Get enquiries that were cancelled in sales stack with user information
            cancelled_enquiries_query = """
                SELECT e.id, e.enquiry_id, e.current_status, e.last_status_change, esh.sales_agent_email
                FROM enquiries e
                LEFT JOIN enquiry_status_history esh ON e.id = esh.enquiry_id 
                    AND esh.status = 'cancelled' 
                    AND esh.created_at = e.last_status_change
                WHERE e.current_status = 'cancelled' 
                AND e.last_status_change >= :execution_time
            """
            
            cancelled_enquiries = self.sales_db.read_data(cancelled_enquiries_query, {"execution_time": QueryRun})
            
            if not cancelled_enquiries:
                logging.info("No cancelled enquiries found to process for sample cancellation")
                return True
            
            logging.info(f"Found {len(cancelled_enquiries)} cancelled enquiries to check for sample cancellation")

            # Only cancel when current sample status is explicitly below 'Sample Ready'
            cancellable_states = [
                'sample_request_received',
                'sample_request_raised'
            ]
            
            cancelled_count = 0
            skipped_count = 0
            
            for enquiry in cancelled_enquiries:
                enquiry_id = enquiry['enquiry_id']
                cancelled_by = enquiry.get('sales_team_member', 'Unknown User')

                # Resolve ProcureStack enquiry UUID from Procure enquiries table using Sales enquiry code
                procure_enquiry_result = self.procure_db.read_data(
                    """
                    SELECT id
                    FROM enquiries
                    WHERE enquiry_id = :sales_enquiry_code
                    LIMIT 1
                    """,
                    {"sales_enquiry_code": enquiry_id}
                )

                if not procure_enquiry_result:
                    logging.info(f"No Procure enquiry found for Sales enquiry code {enquiry_id}; skipping sample cancellation")
                    return

                procure_enquiry_id = procure_enquiry_result[0]['id']

                # Fetch all sample requests for this enquiry
                sample_requests_query = """
                SELECT sr.id, sr.status, sr.enquiry_id
                FROM sample_requests sr
                WHERE sr.enquiry_id = :procure_enquiry_id
                ORDER BY sr.last_status_change DESC
            """

            sample_requests = self.procure_db.read_data(sample_requests_query, {"procure_enquiry_id": procure_enquiry_id})
            
            if not sample_requests:
                logging.info(f"No sample requests found for cancelled enquiry {enquiry_id}")
                return

            for sample_request in sample_requests:
                current_sample_status = sample_request['status']
                sample_id = sample_request['id']
                
                # Check if current sample status is NOT "Sample Ready or above"
                if current_sample_status and current_sample_status.lower() in [state.lower() for state in cancellable_states]:
                    # Update sample status to cancelled
                    update_sample_query = """
                        UPDATE sample_requests 
                        SET status = 'sample_cancelled', 
                            last_status_change = CURRENT_TIMESTAMP,
                            remarks = :remarks
                        WHERE id = :sample_id AND status != 'sample_cancelled'
                    """
                    
                    remarks = f"Enquiry moved to Cancelled by {cancelled_by}"
                    self.procure_db.write_data(update_sample_query, {
                        "sample_id": sample_id,
                        "remarks": remarks
                    })
                    
                    # Insert into sample status history
                    insert_history_query = """
                        INSERT INTO sample_status_history (
                            sample_request_id, 
                            sample_status, 
                            changed_at, 
                            enquiry_id
                        ) VALUES (
                            :sample_id, 
                            'sample_cancelled', 
                            CURRENT_TIMESTAMP, 
                            :enquiry_id
                        )
                        ON CONFLICT (sample_status, sample_request_id) DO NOTHING
                    """
                    
                    self.procure_db.write_data(insert_history_query, {
                        "sample_id": sample_id,
                        "enquiry_id": procure_enquiry_id
                    })

                    # Also reflect cancellation in SalesStack tables
                    update_sales_sample_query = """
                        UPDATE sample_requests 
                        SET status = 'sample_cancelled', 
                            last_status_change = CURRENT_TIMESTAMP,
                            remarks = :remarks
                        WHERE id = :sample_id AND status != 'sample_cancelled'
                    """

                    self.sales_db.write_data(update_sales_sample_query, {
                        "sample_id": sample_id,
                        "remarks": remarks
                    })

                    insert_sales_history_query = """
                        INSERT INTO sample_status_history (
                            sample_request_id, 
                            sample_status, 
                            changed_at, 
                            enquiry_id
                        ) VALUES (
                            :sample_id, 
                            'sample_cancelled', 
                            CURRENT_TIMESTAMP, 
                            :enquiry_id
                        )
                        ON CONFLICT (sample_status, sample_request_id) DO NOTHING
                    """

                    self.sales_db.write_data(insert_sales_history_query, {
                        "sample_id": sample_id,
                        "enquiry_id": enquiry['id']
                    })
                    
                    # If status was 'sample_request_raised', also delete from LogiStack order book
                    if current_sample_status.lower() == 'sample_request_raised':
                        # Fetch employee_id from MongoDB using the cancelled_by email
                        employee_id = self.fetch_employee_id_by_email(cancelled_by)
                        if employee_id:
                            self._delete_logistack_order_book(sample_id, employee_id)
                        else:
                            raise Exception(f"No employee_id found for email {cancelled_by}")
                    
                    logging.info(f"Updated sample {sample_id} to 'sample_cancelled' for cancelled enquiry {enquiry_id} (previous status: {current_sample_status})")
                    cancelled_count += 1
                else:
                    logging.info(f"Sample {sample_id} for cancelled enquiry {enquiry_id} - current status '{current_sample_status}' is Sample Ready or above")
                    skipped_count += 1
            
        logging.info(f"Sample cancellation processing completed: {cancelled_count} samples cancelled, {skipped_count} samples skipped")
        return True
        
        except Exception as e:
        raise Exception(f"Error handling sample cancellation for cancelled enquiries: {str(e)}")       


def create_sync_task(func: callable) -> callable:
    """Create a sync task with proper error handling and logging"""
    def wrapper(*args, **kwargs):
        sync_manager = SalesProcureStatusSyncManager()
        try:
            # Default to PO documents bucket
            with sync_manager.database_connections():
                if not func(sync_manager):
                    raise Exception(f"Failed to execute {func.__name__}")
                logging.info(f"Successfully completed {func.__name__}")
        except Exception as e:
            logging.error(f"Task failed: {str(e)}")
            send_exception_slack_alert(e, kwargs)
            raise
    return wrapper

def create_sync_task_with_bucket(procure_bucket: str, sales_bucket: str, **kwargs):
    """Create a sync task with specific bucket configuration"""
    def decorator(func: callable) -> callable:
        def wrapper(*args, **kwargs):
            sync_manager = SalesProcureStatusSyncManager()
            try:
                with sync_manager.database_connections(
                    procure_bucket_name=procure_bucket,
                    sales_bucket_name=sales_bucket
                ):
                    if not func(sync_manager):
                        raise Exception(f"Failed to execute {func.__name__}")
                    logging.info(f"Successfully completed {func.__name__}")
                # return True 
            except Exception as e:
                logging.error(f"Task failed: {str(e)}")
                send_exception_slack_alert(e, kwargs)
                raise
        return wrapper
    return decorator

@create_sync_task
def sync_enquiry_status(manager: SalesProcureStatusSyncManager) -> bool:
    return manager.update_sales_enquiry_status()

@create_sync_task
def sync_clarification_records(manager: SalesProcureStatusSyncManager) -> bool:
    return manager.sync_clarification_responses()

@create_sync_task
def sync_status_history(manager: SalesProcureStatusSyncManager) -> bool:
    return manager.sync_status_history_tables()

@create_sync_task_with_bucket(
    procure_bucket=SupabaseConfig.STORAGE_BUCKETS['PROCURE_STACK']['PURCHASE_ORDER_DOCUMENTS'],
    sales_bucket=SupabaseConfig.STORAGE_BUCKETS['SALES_STACK']['PURCHASE_ORDER_DOCUMENTS']
)
def sync_purchase_order(manager: SalesProcureStatusSyncManager) -> bool:
    return manager.sync_purchase_order()


@create_sync_task_with_bucket(
    procure_bucket=SupabaseConfig.STORAGE_BUCKETS['PROCURE_STACK']['PURCHASE_ORDER_DOCUMENTS'],
    sales_bucket=SupabaseConfig.STORAGE_BUCKETS['SALES_STACK']['PURCHASE_ORDER_DOCUMENTS']
)
def sync_purchase_order_items(manager: SalesProcureStatusSyncManager) -> bool:
    return manager.sync_purchase_order_items()

@create_sync_task_with_bucket(
    procure_bucket=SupabaseConfig.STORAGE_BUCKETS['PROCURE_STACK']['QUOTATION_DOCUMENTS'],
    sales_bucket=SupabaseConfig.STORAGE_BUCKETS['SALES_STACK']['QUOTATION_DOCUMENTS']
)


def sync_quote_feedback(manager: SalesProcureStatusSyncManager) -> bool:
    return manager.sync_quote_feedback_details()

@create_sync_task_with_bucket(
    procure_bucket=SupabaseConfig.STORAGE_BUCKETS['PROCURE_STACK']['SAMPLE_DOCUMENTS'],
    sales_bucket=SupabaseConfig.STORAGE_BUCKETS['SALES_STACK']['SAMPLE_DOCUMENTS']
)
def sync_sample_feedback(manager: SalesProcureStatusSyncManager) -> bool:
    return manager.sync_sample_feedback_details()

@create_sync_task_with_bucket(
    procure_bucket=SupabaseConfig.STORAGE_BUCKETS['PROCURE_STACK']['SAMPLE_DOCUMENTS'],
    sales_bucket=SupabaseConfig.STORAGE_BUCKETS['SALES_STACK']['SAMPLE_DOCUMENTS']
)
def sync_sample_requests(manager: SalesProcureStatusSyncManager) -> bool:
    return manager.sync_sample_requested_tables()

@create_sync_task
def sync_sample_cancellation(manager: SalesProcureStatusSyncManager) -> bool:
    return manager.sync_sample_cancellation()


# Create the DAG
dag = DAG(
    'sales_to_procure_status_all_sync',
    default_args={
        **DEFAULT_DAG_ARGS,
        'email_on_failure': True,
        'email': ['<EMAIL>'], 
        'on_failure_callback': send_slack_alert
    },
    description='Sync enquiry status and clarification records from SalesStack to ProcureStack',
    schedule=timedelta(minutes=3),
    catchup=False,
    start_date=datetime(2024, 1, 1, tzinfo=timezone.utc),
    tags=['database', 'sync', 'postgres', 'enquiries'],
    dagrun_timeout=timedelta(minutes=10),  # Auto-fail after 30 minutes
    max_active_runs=1,  # No concurrent runs
)

# Create tasks with consistent configuration
tasks = []
for task_id, task_func in [
    ('sync_clarification_records', sync_clarification_records),
    ('sync_enquiry_status', sync_enquiry_status),
    ('sync_status_history', sync_status_history),
    ('sync_purchase_order', sync_purchase_order),
    ('sync_purchase_order_items',sync_purchase_order_items ),
    ('sync_quote_feedback', sync_quote_feedback),
    ('sync_sample_requests', sync_sample_requests),
    ('sync_sample_feedback', sync_sample_feedback),
    ('sync_sample_cancellation', sync_sample_cancellation),
]:
    tasks.append(PythonOperator(
        task_id=task_id,
        python_callable=task_func,
        dag=dag,
        execution_timeout=timedelta(minutes=5),
    ))

# Set task dependencies using chain
chain(*tasks)
