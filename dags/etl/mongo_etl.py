import sys,os
sys.path.insert(0, '/opt/airflow/dag_dependencies')
from db.mongo_operations import MongoOperations
from db.postgres_operations import PostgresOperations
from config.db_config import DatabaseConfig
from dotenv import load_dotenv
import pandas as pd
import logging
import datetime
import sqlalchemy as ss
import random
from datetime import timedelta
from bson import ObjectId

airflow_home_path = os.environ.get('AIRFLOW_HOME')

logging.basicConfig(level=logging.INFO)
print(pd.__version__)
print(ss.__version__)

load_dotenv()

class MongoETL:
    def __init__(self):
        reporting_stack_postgres_params = DatabaseConfig.get_reporting_stack_postgres_params()
        logi_stack_mongo_params = DatabaseConfig.get_logi_stack_mongo_params()
        self.mg_ops = MongoOperations(logi_stack_mongo_params[0], logi_stack_mongo_params[1])
        self.pg_ops = PostgresOperations(reporting_stack_postgres_params)
    def convert_to_usd_value(self, value, currency):
        """Convert a value to USD based on currency and conversion rate"""
        if value is None or pd.isna(value):
            return value
        if pd.isna(currency):
            return value
        # If already in USD, return as is
        if currency and currency.upper() == 'DOLLAR':
            return value
        
        # Default conversion rates for common currencies
        default_rates = {
            'RUPEE': 0.011723,  
            'AED': 0.272259,
            'EURO': 1.123973,  
            'YEN' : 0.00685,
            'YUAN' : 0.141456
        }
        
        if currency and currency.upper() in default_rates:
            return round(value * default_rates[currency.upper()],2)
        
        # If we can't convert, return original value 
        return value

    def get_customer_address(self, address):
        try:
            street = address.get('street','')
            state = address.get('state','')
            country = address.get('country','')
            postal_code = address.get('postalCode','')
            city = address.get('city','')
            return [street, state, country, postal_code, city]
        except:
            return [None, None, None, None, None]


    def get_customers(self, last_updated_timestamp):
        '''
        This function is used to get the customer data from the mongo db and return it as a dataframe.
        '''

        cols_to_query = {'_id':1 , 'customerId':1, 'name':1 , 'address':1, 'email':1, 'size':1 , 'accountOwner':1, 'categories':1, 'type':1,'deleted':1,'createdBy':1, 'lastUpdatedBy':1,'createdAt':1, 'lastUpdatedAt':1}

        customer_data = self.mg_ops.get_collection('customer').find({
             "$or": [
                {"lastUpdatedAt": {"$gt": last_updated_timestamp}},
                {
                    "$and": [
                        {"lastUpdatedAt": None},
                        {"createdAt": {"$gt": last_updated_timestamp}}
                    ]
                }
            ]
        }, cols_to_query)

        df_customer = pd.DataFrame(customer_data)
        logging.info(f"Customer data fetched successfully. Total records: {df_customer.shape}")
        if df_customer.empty:
            return pd.DataFrame()
        for cols, val in cols_to_query.items():
            if cols not in df_customer.columns:
                df_customer[cols] = None
        df_customer[['street','state', 'country','postal_code','city']] = df_customer.apply(lambda x : self.get_customer_address(x['address']), axis=1, result_type='expand')
        df_customer['categories'] = df_customer['categories'].apply(lambda x : ','.join(x) if isinstance(x, list) else None)
        df_customer = df_customer.drop(columns=['address'], axis=1)
        df_customer = df_customer.rename(columns={
            '_id' : 'customer_uid',
            'customerId' : 'customer_id',
            'name' : 'customer_name',
            'createdBy' : 'created_by',
            'lastUpdatedBy' : 'updated_by',
            'createdAt' : 'created_at',
            'lastUpdatedAt' : 'updated_at',
            'accountOwner' : 'account_owner'

        })

        df_customer['created_at'] = pd.to_datetime(df_customer['created_at'], utc=True)
        df_customer['updated_at'] = pd.to_datetime(df_customer['updated_at'], utc=True)
        df_customer = df_customer.astype({
            'customer_uid' : 'string',
            'created_by' : 'string',
            'customer_name' : 'string',
            'updated_by' : 'string',
            'email' : 'string',
            'account_owner' : 'string',
            'size' : 'string',
            'street' : 'string',
            'state' : 'string',
            'country' : 'string',
            'postal_code' : 'string',
            'city' : 'string',
            'categories' : 'string',
            'type' : 'string',
            'deleted' : 'bool',
            'customer_id' : 'string',
        })
        
        return df_customer
    
    def get_employees(self, last_updated_timestamp):
        '''
        This function is used to get the employee data from the mongo db and return it as a dataframe.
        '''

        cols_to_query = {
            '_id': 1,
            'employeeId': 1,
            'name': 1,
            'email': 1,
            'createdAt': 1,
            'lastUpdatedAt': 1,
            'deleted': 1,
            'team': 1,
            'firstName': 1,
            'lastName': 1,
            'mobileNumber': 1,
            'countryCode': 1,
            'vertical': 1,
            'level': 1,
            'lastUpdatedBy': 1
        }

        employee_data = self.mg_ops.get_collection('employee').find({
            "$or": [
                {"lastUpdatedAt": {"$gt": last_updated_timestamp}},
                {
                    "$and": [
                        {"lastUpdatedAt": None},
                        {"createdAt": {"$gt": last_updated_timestamp}}
                    ]
                }
            ]
        }, cols_to_query)

        df_employee = pd.DataFrame(employee_data)
        logging.info(f"Employee data fetched successfully. Total records: {df_employee.shape}")

        if df_employee.empty:
            return pd.DataFrame()

        # Ensure all expected columns are present
        for col in cols_to_query:
            if col not in df_employee.columns:
                df_employee[col] = None

        # Rename columns
        df_employee = df_employee.rename(columns={
            '_id': 'employee_id',
            'employeeId': 'employee_code',
            'createdAt': 'created_at',
            'lastUpdatedAt': 'last_updated_at',
            'firstName': 'firstname',
            'lastName': 'lastname',
            'mobileNumber': 'mobilenumber',
            'countryCode': 'countrycode',
            'vertical': 'vertical',
            'level': 'level',
            'lastUpdatedBy': 'lastupdatedby'
        })

        # Convert datetime fields
        df_employee['created_at'] = pd.to_datetime(df_employee['created_at'], utc=True)
        df_employee['last_updated_at'] = pd.to_datetime(df_employee['last_updated_at'], utc=True)

        # Enforce schema
        df_employee = df_employee.astype({
            'employee_id': 'string',
            'employee_code': 'string',
            'name': 'string',
            'email': 'string',
            'team' : 'string',
            'firstname' : 'string',
            'lastname' : 'string',
            'mobilenumber' : 'string',
            'countrycode' : 'string',
            'vertical' : 'string',
            'level' : 'string',
            'lastupdatedby' : 'string',
            'deleted': 'bool'
        })

        return df_employee


    def get_customer_order_products(self, products_df : pd.DataFrame, buyer_currency_df : pd.DataFrame):
       
        df_customer_order_products = products_df.explode('products').reset_index(drop=True)
        
        df_customer_order_products_info = pd.json_normalize(df_customer_order_products['products'])
        df_customer_order_products = pd.concat([df_customer_order_products.drop(columns=['products'], axis=1), df_customer_order_products_info], axis=1)
        df_customer_order_products = df_customer_order_products.merge(
            buyer_currency_df,
            on='customer_order_uid',
            how='left'
        )
        found = False
        for col in df_customer_order_products.columns:
            if 'product.' in col:
                found = True
                break

        if df_customer_order_products.empty or not found:
            return pd.DataFrame()
        cols_to_present = ['_id','uom','quantityPerUnit','perUnitWeight','product._id','packaging._id','chemstackPrice','perUnitKgValue','hsCode','stateOfOrigin','batchData','districtOfOrigin','status','countryOfOrigin', 'order_value']
        for col in cols_to_present:
            if col not in df_customer_order_products.columns:
                df_customer_order_products[col] = None
        df_customer_order_products['_id'] = df_customer_order_products['_id'].fillna('')
        df_customer_order_products = df_customer_order_products.rename(columns={
            '_id': 'customer_order_product_id',
            'uom': 'unit_of_measure',
            'quantityPerUnit': 'quantity_per_unit',
            'perUnitWeight': 'per_unit_weight',
            'product._id' : 'product_id',
            'packaging._id' : 'packaging_id',
            'chemstackPrice' : 'chemstack_price',
            'perUnitKgValue' : 'per_unit_kg_value',
            'hsCode' : 'hs_code',
            'stateOfOrigin' : 'state_of_origin',
            'batchData' : 'batch_data',
            'districtOfOrigin' : 'district_of_origin',
            'countryOfOrigin' : 'country_of_origin'
        })
        for col in ['product.','packaging.','labelFile.','productNameAlias','hazDetails','batchDateVisible','remarks']:
            for cols in df_customer_order_products.columns:
                if col in cols:
                    df_customer_order_products = df_customer_order_products.drop(columns=[cols], axis=1)
        df_customer_order_products['order_value'] = df_customer_order_products.apply(
            lambda x: self.convert_to_usd_value(x['price'] * x['quantity'], x['buyer_currency']),
            axis=1
        )
        df_customer_order_products = df_customer_order_products.astype({
            'customer_order_product_id' : 'string',
            'unit_of_measure' : 'string',
            'quantity_per_unit' : 'float64',
            'per_unit_weight' : 'float64',
            'product_id' : 'string',
            'packaging_id' : 'string',
            'chemstack_price' : 'float64',
            'per_unit_kg_value' : 'float64',
            'hs_code' : 'string',
            'state_of_origin' : 'string',
            'batch_data' : 'string',
            'district_of_origin' : 'string',
            'country_of_origin' : 'string',
            'status' : 'string',
            'order_value' : 'float64'           
        })
        
        cols_to_delete = ['batch_data','buyer_currency']
        for col in cols_to_delete:
            if col in df_customer_order_products.columns:
                df_customer_order_products = df_customer_order_products.drop(columns=[col], axis=1)
        
        return df_customer_order_products
        
    def get_customer_order(self, last_updated_timestamp):
        """
        This function is used to get the customer order data from the mongo db and return it as a dataframe.
        """
        cols_to_query = {'_id':1, 'orderId':1,'purchaseOrderNumber':1,'purchaseOrderDate':1,'customer': 1, 'products':1, 'status':1 , 'deleted':1, 'approved':1,'createdAt':1, 'createdBy':1, 'invoiceDate':1,'lastUpdatedAt':1, 'lastUpdatedBy':1,
        'customerAppointementDate' : 1,'expectedDeliveryDate' : 1,'orderDeliveredOn' : 1,'chemstackInvoiceDate' : 1, 'notifyParty' : 1,
                         'typeOfBL':1,'incoterms':1,'shipmentDate':1,'deliveryDate':1, 'paymentTerms':1, 'marginPercentage': 1, 'supplierRfq': 1, 'buyerCurrency': 1, 'orderDeliveredOn' : 1, 'trackingUrl': 1, 'sampleDeliveredOn' :1}
        

        customer_order_data = self.mg_ops.get_collection('customerOrder').find({
            "$and": [
                {
                    "$or": [
                        {"lastUpdatedAt": {"$gt": last_updated_timestamp}},
                        {
                            "$and": [
                                {"lastUpdatedAt": None},
                                {"createdAt": {"$gt": last_updated_timestamp}}
                            ]
                        }
                    ]
                }
            ]
        }, cols_to_query)

        df_customer_order = pd.DataFrame(customer_order_data)
        logging.info(f"Customer order data fetched successfully. Total records: {df_customer_order.shape}")
        if df_customer_order.empty:
            return pd.DataFrame(), pd.DataFrame()
        
        for cols, val in cols_to_query.items():
            if cols not in df_customer_order.columns:
                df_customer_order[cols] = None
        
        df_customer_order['customer_id'] = df_customer_order['customer'].apply(lambda x : x.get('_id') if pd.notna(x) else None)
        df_customer_order[['creditor_days', 'credit_amount', 'start_date']] = df_customer_order.apply(lambda x : self.get_order_payment_terms(x['paymentTerms']), axis=1, result_type='expand')
        df_customer_order[['inco_type', 'inco_country', 'inco_shipment_method', 'inco_port']] = df_customer_order.apply(lambda x : self.get_order_book_inco_terms(x['incoterms']), axis=1, result_type='expand')       
        def extract_supplier_rfq_fields(supplier_rfq):
            try:
                if pd.isna(supplier_rfq) or not supplier_rfq:
                    return [None] * 19
                purchase_order = supplier_rfq.get('purchaseOrderValue', {}) or {}
                logistics_cost = supplier_rfq.get('logisticsCost', {}) or {}
                duty_amount = supplier_rfq.get('dutyAmountValue', {}) or {}
                sales_order = supplier_rfq.get('salesOrderValue', {}) or {}
                
                # Convert values to USD
                po_value_usd = convert_to_usd(purchase_order.get('value'), purchase_order.get('currency'))
                logistics_value_usd = convert_to_usd(logistics_cost.get('value'), logistics_cost.get('currency'))
                duty_value_usd = convert_to_usd(duty_amount.get('value'), duty_amount.get('currency'))
                sales_value_usd = convert_to_usd(sales_order.get('value'), sales_order.get('currency'))

                currency = logistics_cost.get("currency")
                logistics_currency = logistics_cost.get("currency")
                country_of_origin = supplier_rfq.get("countryOfOrigin")

                # Logistics components (always 4)
                logistics_fields = ['firstMileLogistics', 'seaFreight', 'lastMileLogistics', 'destinationCharges']
                converted_logistics = [
                    convert_to_usd(supplier_rfq.get(field), currency) if supplier_rfq.get(field) is not None else None
                    for field in logistics_fields
                ]
                if len(converted_logistics) != 4:
                    converted_logistics = (converted_logistics + [None] * 4)[:4]

                result = [
                    po_value_usd, purchase_order.get('currency'), purchase_order.get('conversionRate'),
                    logistics_value_usd, logistics_cost.get('currency'), logistics_cost.get('conversionRate'),
                    duty_value_usd, duty_amount.get('currency'), duty_amount.get('conversionRate'),
                    sales_value_usd, sales_order.get('currency'), sales_order.get('conversionRate'),
                    supplier_rfq.get('targetContributionMargin'),
                    country_of_origin,
                    *converted_logistics,
                    logistics_currency
                ]

                # Force length to 18
                return (result + [None] * 19)[:19]

            except Exception as e:
                # Fail-safe for unexpected structure
                return [None] * 19


        def convert_to_usd(value, currency):
            """Convert a value to USD based on currency and conversion rate"""
            if value is None:
                return None        
            # If already in USD, return as is
            if currency and currency.upper() == 'USD':
                return value                   
            default_rates = {
                'INR': 0.011723,  
                'AED': 0.272259,
                'EUR': 1.123973,  
                'JPY' : 0.00685,
                'CNY' : 0.141456
            }           
            if currency and currency.upper() in default_rates:
                return value * default_rates[currency.upper()]
        
            return value
        # Apply the extraction function
        df_customer_order[['purchase_order_value', 'purchase_order_currency', 'purchase_order_conversion_rate',
                          'logistics_cost_value', 'logistics_cost_currency', 'logistics_cost_conversion_rate',
                          'duty_amount_value', 'duty_amount_currency', 'duty_amount_conversion_rate',
                          'sales_order_value', 'sales_order_currency', 'sales_order_conversion_rate',
                          'target_contribution_margin','country_of_origin','first_mile_logistics','sea_freight','last_mile_logistics','destination_charges','logistics_currency']] = df_customer_order.apply(
            lambda x: extract_supplier_rfq_fields(x['supplierRfq']), axis=1, result_type='expand')
        
        df_customer_order = df_customer_order.rename(columns={
            '_id' : 'customer_order_uid',
            'orderId' : 'order_id',
            'purchaseOrderNumber' : 'purchase_order_number',
            'purchaseOrderDate' : 'purchase_order_date',
            'createdAt' : 'created_at',
            'createdBy' : 'created_by',
            'invoiceDate' : 'invoice_date',
            'lastUpdatedAt' : 'updated_at',
            'lastUpdatedBy' : 'updated_by',
            'typeOfBL' : 'type_of_bl',
            'incoterms' : 'incoterms',
            'shipmentDate' : 'shipment_date',
            'deliveryDate' : 'delivery_date',
            'marginPercentage' : 'margin_percentage',
            'buyerCurrency' : 'buyer_currency',
            'trackingUrl' : 'tracking_url',
            'sampleDeliveredOn' : 'sample_delivered_on',
            'customerAppointementDate' : 'customer_appointment_date',
            'expectedDeliveryDate' : 'expected_delivery_date',
            'orderDeliveredOn' : 'order_delivered_on',
            'chemstackInvoiceDate' : 'chemstack_invoice_date',
            'notifyParty' : 'notify_party'
        })
        df_customer_order['purchase_order_number'] = df_customer_order['purchase_order_number'].str.strip()
        df_customer_order['created_at'] = pd.to_datetime(df_customer_order['created_at'], utc=True)
        df_customer_order['updated_at'] = pd.to_datetime(df_customer_order['updated_at'], utc=True)
        df_customer_order['invoice_date'] = pd.to_datetime(df_customer_order['invoice_date'], utc=True)
        df_customer_order['shipment_date'] = pd.to_datetime(df_customer_order['shipment_date'], utc=True)
        df_customer_order['delivery_date'] = pd.to_datetime(df_customer_order['delivery_date'], utc=True)
        df_customer_order['purchase_order_date'] = pd.to_datetime(df_customer_order['purchase_order_date'], utc=True)
        df_customer_order['sample_delivered_on'] = pd.to_datetime(df_customer_order['sample_delivered_on'], utc=True)
        df_customer_order['customer_appointment_date'] = pd.to_datetime(df_customer_order['customer_appointment_date'], utc=True)
        df_customer_order['expected_delivery_date'] = pd.to_datetime(df_customer_order['expected_delivery_date'], utc=True)
        df_customer_order['order_delivered_on'] = pd.to_datetime(df_customer_order['order_delivered_on'], utc=True)
        df_customer_order['chemstack_invoice_date'] = pd.to_datetime(df_customer_order['chemstack_invoice_date'], utc=True)
        df_customer_order = df_customer_order.astype({
            'customer_order_uid' : 'string',
            'customer_id' : 'string',
            'created_by' : 'string',
            'updated_by' : 'string',
            'type_of_bl' : 'string',
            'approved' : 'bool',
            'status' : 'string',
            'deleted' : 'bool',
            'credit_amount' : 'float64',
            'start_date' : 'string',
            'inco_type' : 'string',
            'inco_country' : 'string',
            'inco_shipment_method' : 'string',
            'inco_port' : 'string',
            'order_id' : 'string',
            'purchase_order_number' : 'string',
            'margin_percentage' : 'float64',
            'buyer_currency' : 'string',
            'purchase_order_value' : 'float64',
            'purchase_order_currency' : 'string',
            'purchase_order_conversion_rate' : 'float64',
            'logistics_cost_value' : 'float64',
            'logistics_cost_currency' : 'string',
            'logistics_cost_conversion_rate' : 'float64',
            'duty_amount_value' : 'float64',
            'duty_amount_currency' : 'string',
            'duty_amount_conversion_rate' : 'float64',
            'sales_order_value' : 'float64',
            'sales_order_currency' : 'string',
            'sales_order_conversion_rate' : 'float64',
            'target_contribution_margin' : 'float64',
            'country_of_origin' : 'string',
            'first_mile_logistics' : 'float64',
            'sea_freight' : 'float64',
            'last_mile_logistics' : 'float64',
            'destination_charges' : 'float64',
            'notify_party' : 'string',
            'tracking_url' : 'string'
        })
        
        df_customer_order = df_customer_order.drop(columns=['supplierRfq'], axis=1)
        
        df_customer_order_products = self.get_customer_order_products(df_customer_order[['products','customer_order_uid']], df_customer_order[['customer_order_uid','buyer_currency']])

        df_customer_order = df_customer_order.drop(columns=['products','customer','paymentTerms','incoterms'], axis=1)

        customer_order_cols = [
            'customer_order_uid',
            'order_id',
            'purchase_order_number', 
            'purchase_order_date',
            'status',
            'deleted',
            'approved',
            'created_at',
            'created_by',
            'invoice_date',
            'updated_at',
            'updated_by',
            'type_of_bl',
            'shipment_date', 
            'delivery_date',
            'customer_id',
            'creditor_days',
            'credit_amount',
            'start_date',
            'inco_type',
            'inco_country',
            'inco_shipment_method',
            'inco_port',
            'buyer_currency',
            'margin_percentage',
            # Add the supplierRfq extracted fields
            'purchase_order_value',
            'purchase_order_currency',
            'purchase_order_conversion_rate',
            'logistics_cost_value',
            'logistics_cost_currency',
            'logistics_cost_conversion_rate',
            'duty_amount_value',
            'duty_amount_currency',
            'duty_amount_conversion_rate',
            'sales_order_value',
            'sales_order_currency',
            'sales_order_conversion_rate',
            'target_contribution_margin',
            'country_of_origin',
            'first_mile_logistics',
            'sea_freight',
            'last_mile_logistics',
            'destination_charges',
            'tracking_url',
            'sample_delivered_on',
            'order_delivered_on',
            'customer_appointment_date',
            'expected_delivery_date',
            'chemstack_invoice_date',
            'notify_party'

        ]
        for col in customer_order_cols:
            if col not in df_customer_order.columns:
                df_customer_order[col] = None
        
        df_customer_order = df_customer_order.astype({
            'creditor_days' : 'float64',
        })
        df_customer_order = df_customer_order[customer_order_cols]

        customer_order_product_cols = [
            'customer_order_uid',
            'customer_order_product_id',
            'unit_of_measure',
            'quantity',
            'price',
            'units', 
            'status',
            'quantity_per_unit',
            'per_unit_weight',
            'product_id',
            'packaging_id', 
            'hs_code',
            'district_of_origin',
            'state_of_origin',
            'chemstack_price',
            'per_unit_kg_value',
            'country_of_origin',
            'order_value'
        ]
        for col in customer_order_product_cols:
            if col not in df_customer_order_products.columns:
                df_customer_order_products[col] = None  

        df_customer_order_products = df_customer_order_products[customer_order_product_cols]
        
        return df_customer_order, df_customer_order_products
    
    def get_inventory_product(self, last_updated_timestamp):
        """
        This function is used to get the inventory product data from the mongo db and return it as a dataframe.
        """
        cols_to_query = {'_id':1, 'inventoryId':1, 'productId':1, 'productName':1, 'packagingId':1, 'units':1 , 'deleted':1, 'createdAt':1, 'createdBy':1, 'lastUpdatedAt':1, 'lastUpdatedBy':1}

        inventory_product_data = self.mg_ops.get_collection('inventoryProduct').find({
             "$or": [
                {"lastUpdatedAt": {"$gt": last_updated_timestamp}},
                {
                    "$and": [
                        {"lastUpdatedAt": None},
                        {"createdAt": {"$gt": last_updated_timestamp}}
                    ]
                }
            ]
        }, cols_to_query)
        df_inventory_product = pd.DataFrame(inventory_product_data)
        logging.info(f"Inventory product data fetched successfully. Total records: {df_inventory_product.shape}")
        if df_inventory_product.empty:
            return pd.DataFrame()
        
        for cols, val in cols_to_query.items():
            if cols not in df_inventory_product.columns:
                df_inventory_product[cols] = None
        df_inventory_product = df_inventory_product.rename(columns={
            '_id' : 'inventory_product_uid',
            'inventoryId' : 'inventory_id',
            'productId' : 'product_id',
            'productName' : 'product_name',
            'packagingId' : 'packaging_id',
            'units' : 'units',
            'createdAt' : 'created_at',
            'createdBy' : 'created_by',
            'lastUpdatedAt' : 'updated_at',
            'lastUpdatedBy' : 'updated_by'
        })
        df_inventory_product['created_at'] = pd.to_datetime(df_inventory_product['created_at'], utc=True)
        df_inventory_product['updated_at'] = pd.to_datetime(df_inventory_product['updated_at'], utc=True)
        df_inventory_product = df_inventory_product.astype({
            'inventory_product_uid' : 'string',
            'inventory_id' : 'string',
            'product_id' : 'string',
            'product_name' : 'string',
            'packaging_id' : 'string',
            'units' : 'int64',
            'created_by' : 'string',
            'updated_by' : 'string'
        })
        
        return df_inventory_product
    
    def get_inventory_meta(self, meta):
        try:
            ordered_product_id = meta.get('orderedProductId', None)
            order_book_id = meta.get('orderBookId', None)
            purchase_order_number = meta.get('purchaseOrderNumber', None)
            supplier_order_book_id = meta.get('supplierOrderbookId', None)
            return [ordered_product_id, order_book_id, purchase_order_number, supplier_order_book_id]
        except:
            return [None, None, None, None]
    
    def get_inventory_product_transaction(self, last_updated_timestamp):
        """
        This function is used to get the inventory product transaction data from the mongo db and return it as a dataframe.
        """
        cols_to_query = {'_id':1, 'transactionId':1, 'inventoryProductId':1, 'units':1, 'meta':1 , 'transactionType':1   , 'deleted':1, 'createdAt':1, 'createdBy':1, 'lastUpdatedAt':1, 'lastUpdatedBy':1}

        inventory_product_transaction_data = self.mg_ops.get_collection('inventoryProductTransaction').find({
             "$or": [
                {"lastUpdatedAt": {"$gt": last_updated_timestamp}},
                {
                    "$and": [
                        {"lastUpdatedAt": None},
                        {"createdAt": {"$gt": last_updated_timestamp}}
                    ]
                }
            ]
        }, cols_to_query)
        df_inventory_product_transaction = pd.DataFrame(inventory_product_transaction_data)
        logging.info(f"Inventory product transaction data fetched successfully. Total records: {df_inventory_product_transaction.shape}")
        if df_inventory_product_transaction.empty:
            return pd.DataFrame()
        
        for cols, val in cols_to_query.items():
            if cols not in df_inventory_product_transaction.columns:
                df_inventory_product_transaction[cols] = None

        df_inventory_product_transaction[['ordered_product_id', 'order_book_id', 'purchase_order_number', 'supplier_order_book_id']] = df_inventory_product_transaction.apply(lambda x : self.get_inventory_meta(x['meta']), axis=1, result_type='expand')

        df_inventory_product_transaction = df_inventory_product_transaction.drop(columns=['meta'], axis=1)

        df_inventory_product_transaction = df_inventory_product_transaction.rename(columns={
            '_id' : 'inventory_product_transaction_uid',
            'transactionId' : 'transaction_id',
            'inventoryProductId' : 'inventory_product_id',
            'units' : 'units',
            'meta' : 'meta',
            'transactionType' : 'transaction_type',
            'createdAt' : 'created_at',
            'createdBy' : 'created_by',
            'lastUpdatedAt' : 'updated_at',
            'lastUpdatedBy' : 'updated_by'
        })
        df_inventory_product_transaction['created_at'] = pd.to_datetime(df_inventory_product_transaction['created_at'], utc=True)
        df_inventory_product_transaction['updated_at'] = pd.to_datetime(df_inventory_product_transaction['updated_at'], utc=True)
        df_inventory_product_transaction = df_inventory_product_transaction.astype({
            'inventory_product_transaction_uid' : 'string',
            'inventory_product_id' : 'string',
            'transaction_id' : 'string',
            'transaction_type' : 'string',
            'created_by' : 'string',
            'ordered_product_id' : 'string',
            'order_book_id' : 'string',
            'purchase_order_number' : 'string',
            'supplier_order_book_id' : 'string',
            'deleted' : 'bool',
            'updated_by' : 'string'
        })
        
        return df_inventory_product_transaction
    
    def get_order_payment_terms(self, payment_terms):
        try:
            creditor_days = payment_terms.get('creditorDays', None)
            credit_amount = payment_terms.get('creditAmount', None)
            start_date = payment_terms.get('poPaymentTerms', None) or payment_terms.get('startDate', None)
            return [creditor_days, credit_amount, start_date]
        except:
            return [None, None, None]
    
    def get_so_order_book_payment_terms(self, payment_terms):
        try:
            creditor_days = payment_terms.get('creditorDays', None)
            credit_amount = payment_terms.get('creditAmount', None)
            advance_amount = payment_terms.get('advanceAmount', None)
            po_payment_terms = payment_terms.get('poPaymentTerms', None)
            return [creditor_days, credit_amount, advance_amount, po_payment_terms]
        except:
            return [None, None, None, None]
        
    def get_order_book_inco_terms(self, incoterms):
        try:
            inco_type = incoterms.get('type', None)
            inco_country = incoterms.get('country', None)
            inco_shipment_method = incoterms.get('data', {}).get('shipmentMethod', None)
            inco_port = incoterms.get('data', {}).get('portOfDischarge', None)
            return [inco_type, inco_country, inco_shipment_method, inco_port]
        except:
            return [None, None, None, None]
    
    def get_order_book_batch(self, last_updated_timestamp):
        """
        This function is used to get the order book batch data from the mongo db and return it as a dataframe.
        """
        cols_to_query = {'_id':1, 'supplierOrderBookId':1, 'inventoryInOrderId':1, 'inventoryId':1, 'packagingId':1, 'unitOfMeasure':1, 'productId':1, 'type':1, 'deleted':1,
        'createdAt':1, 'createdBy':1,  'lastUpdatedAt':1, 'lastUpdatedBy':1}
        order_book_batch_data = self.mg_ops.get_collection('oBProductBatch').find({
             "$or": [
                {"lastUpdatedAt": {"$gt": last_updated_timestamp}},
                {
                    "$and": [
                        {"lastUpdatedAt": None},
                        {"createdAt": {"$gt": last_updated_timestamp}}
                    ]
                }
            ]
        }, cols_to_query)
        df_order_book_batch = pd.DataFrame(order_book_batch_data)
        logging.info(f"Order book batch data fetched successfully. Total records: {df_order_book_batch.shape}")
        if df_order_book_batch.empty:
            return pd.DataFrame()
        
        for cols, val in cols_to_query.items():
            if cols not in df_order_book_batch.columns:
                df_order_book_batch[cols] = None
            

        df_order_book_batch = df_order_book_batch.rename(columns={
            '_id' : 'order_book_batch_uid',
            'supplierOrderBookId' : 'supplier_order_book_id',
            'inventoryInOrderId' : 'inventory_in_order_id',
            'inventoryId' : 'inventory_id',
            'packagingId' : 'packaging_id',
            'unitOfMeasure' : 'unit_of_measure',
            'productId' : 'product_id',
            'type' : 'type',
            'createdAt' : 'created_at',
            'createdBy' : 'created_by',
            'lastUpdatedAt' : 'updated_at',
            'lastUpdatedBy' : 'updated_by'
        })

        df_order_book_batch['created_at'] = pd.to_datetime(df_order_book_batch['created_at'], utc=True)
        df_order_book_batch['updated_at'] = pd.to_datetime(df_order_book_batch['updated_at'], utc=True)

        df_order_book_batch = df_order_book_batch.astype({
            'order_book_batch_uid' : 'string',
            'supplier_order_book_id' : 'string',
            'inventory_in_order_id' : 'string',
            'inventory_id' : 'string',
            'packaging_id' : 'string',
            'unit_of_measure' : 'string',
            'product_id' : 'string',
            'type' : 'string',
            'created_by' : 'string',
            'updated_by' : 'string',
            'deleted' : 'bool'
        })
        
        return df_order_book_batch

    
    def get_order_book_products(self, products_df : pd.DataFrame, buyer_currency_df : pd.DataFrame):
        
        products_df = products_df.rename(columns={'_id':'order_book_uid'})
        df_order_book_products = products_df.explode('products').reset_index(drop=True)
        df_order_book_products_info = pd.json_normalize(df_order_book_products['products'], sep='.')
        df_order_book_products = pd.concat([df_order_book_products.drop(columns=['products'], axis=1), df_order_book_products_info], axis=1)
        # Rename _id in buyer_currency_df to avoid overwrite
        buyer_currency_df = buyer_currency_df.rename(columns={'_id': 'buyer_order_id'})


        df_order_book_products = df_order_book_products.merge(
            buyer_currency_df,
            left_on='order_book_uid',
            right_on='buyer_order_id',
            how='left'
        )

       

        found = False
        for col in df_order_book_products.columns:
            if 'product.' in col:
                found = True
                break

        if df_order_book_products.empty or not found:
            return pd.DataFrame()
        cols_to_be_present = ['incoterms.type', 'incoterms.country', 'incoterms.data.shipmentMethod','incoterms.data.portOfDischarge','incoterms.data.placeOfDelivery', 'incoterms.data.portOfLoading','incoterms.data.city','incoterms.data.placeOfPickup'
                              ,'shipmentDate','deliveryDate','expectedDeliveryDate','hsCode','packaging._id','quantityPerUnit','packaging.otherPackagingDetails',
                              'incoterms.data.destinationCountry','modeOfExport','deliveryInstructions',
                            'testingRequired','dispatchWithResults' ]
        for col in cols_to_be_present:
            if col not in df_order_book_products.columns:
                df_order_book_products[col] = None
        df_order_book_products = df_order_book_products.rename(columns={
            '_id': 'order_book_product_id',
            'uom': 'unit_of_measure',
            'product._id': 'product_id',
            'packaging._id': 'packing_id',
            'hsCode': 'hs_code',
            'margin': 'margin',
            'units': 'units',
            'quantity': 'quantity',
            'price': 'price',
            'incoterms.type': 'inco_type',
            'incoterms.country': 'inco_country',
            'incoterms.data.shipmentMethod': 'inco_shipment_method',
            'incoterms.data.portOfDischarge': 'inco_port',
            'incoterms.data.placeOfDelivery': 'inco_place_of_delivery',
            'incoterms.data.portOfLoading': 'inco_port_of_loading',
            'incoterms.data.destinationCountry': 'inco_destination_country',
            'incoterms.data.city': 'inco_city',
            'incoterms.data.placeOfPickup': 'inco_place_of_pickup',
            'shipmentDate': 'shipment_date',
            'deliveryDate': 'delivery_date',
            'expectedDeliveryDate': 'expected_delivery_date',
            'buyerCurrency' : 'buyer_currency',
            'quantityPerUnit': 'quantity_per_unit',
            'packaging.otherPackagingDetails': 'packaging_other_details',
            'testingRequired' : 'testing_required',
            'dispatchWithResults' : 'dispatch_with_results',
            'modeOfExport' : 'mode_of_export',
            'deliveryInstructions' : 'delivery_instructions'

        })

        df_order_book_products['shipment_date'] = pd.to_datetime(df_order_book_products['shipment_date'], utc=True)
        df_order_book_products['delivery_date'] = pd.to_datetime(df_order_book_products['delivery_date'], utc=True)
        df_order_book_products['expected_delivery_date'] = pd.to_datetime(df_order_book_products['expected_delivery_date'], utc=True)
        df_order_book_products['order_value'] = df_order_book_products.apply(lambda x : self.convert_to_usd_value(x['price'] * x['quantity'],x['buyer_currency']), axis=1)
        for col in ['product.','packaging.','incoterms.']:
            for cols in df_order_book_products.columns:
                if col in cols:
                    df_order_book_products = df_order_book_products.drop(columns=[cols], axis=1)

        df_order_book_products = df_order_book_products.astype({
            'order_book_uid' : 'string',
            'order_book_product_id' : 'string',
            'product_id' : 'string',
            'packing_id' : 'string',
            'inco_type' : 'string',
            'inco_country' : 'string',
            'inco_shipment_method' : 'string',
            'inco_port' : 'string',
            'inco_place_of_delivery' : 'string',
            'inco_port_of_loading' : 'string',
            'inco_city' : 'string',
            'inco_place_of_pickup' : 'string',
            'inco_destination_country' : 'string',
            'hs_code' : 'string',
            'margin' : 'float64',
            'unit_of_measure' : 'string',
            'price' : 'float64',
            'quantity' : 'float64',
            'order_value' : 'float64',
            'buyer_currency' : 'string',
            'quantity_per_unit' : 'float64',
            'packaging_other_details' : 'string',
            'testing_required' : 'bool',
            'dispatch_with_results' : 'bool',
            'mode_of_export' : 'string',
            'delivery_instructions' : 'string', 

        })

        cols_to_drop = ['customerProductCode','shipmentDate','documents.TDS','modeOfExport','documents.COA','remarks','documents.SDS','buyer_order_id']

        for col in cols_to_drop:
            if col in df_order_book_products.columns:
                df_order_book_products = df_order_book_products.drop(columns=[col], axis=1)
                
        for col in df_order_book_products.columns:
            if df_order_book_products[col].apply(lambda x: isinstance(x, ObjectId)).any():
                df_order_book_products[col] = df_order_book_products[col].astype(str)       
        return df_order_book_products
        
    
    def get_order_book(self, last_updated_timestamp):
        """
        This function is used to get the order book data from the mongo db and return it as a dataframe.
        """
        cols_to_query = {'_id':1, 'orderBookId':1, 'purchaseOrderNumber':1, 'purchaseOrderDate':1,'customer': 1, 'products':1, 'paymentTerms':1  , 'deleted':1, 'createdAt':1, 'createdBy':1, 
                         'lastUpdatedAt':1, 'lastUpdatedBy':1, 'buyerCurrency' : 1, 'category': 1, 'orderType': 1, 'poValue' :1,'inventoryId': 1,
                           'taxPercent': 1, 'countryOfDelivery': 1,'sampleRequestedBy':1,'deliveryInstructions':1, 'deliveryAddress':1, 'testKey': 1}

        order_book_data = self.mg_ops.get_collection('orderBook').find({
            "$and": [
                {
                    "$or": [
                       {"lastUpdatedAt": {"$gt": last_updated_timestamp}},
                        {
                            "$and": [
                                {"lastUpdatedAt": None},
                                {"createdAt": {"$gt": last_updated_timestamp}}
                            ]
                        }
                    ]
                }
            ]
            
        }, cols_to_query)

        df_order_book = pd.DataFrame(order_book_data)
        logging.info(f"Order book data fetched successfully. Total records: {df_order_book.shape}")
        if df_order_book.empty:
            return pd.DataFrame(), pd.DataFrame()
        
        for cols, val in cols_to_query.items():
            if cols not in df_order_book.columns:
                df_order_book[cols] = None

        df_order_book['customer_id'] = df_order_book['customer'].apply(lambda x : x['_id'] if pd.notna(x) else None)

        df_order_book[['creditor_days', 'credit_amount', 'start_date']] = df_order_book.apply(lambda x : self.get_order_payment_terms(x['paymentTerms']), axis=1, result_type='expand')

        if 'products' in df_order_book.columns and df_order_book['products'].apply(lambda x: isinstance(x, list) and len(x) > 0).any():
            df_order_book_products = self.get_order_book_products(df_order_book[['products','_id']], df_order_book[['_id','buyerCurrency']])
        else:
            df_order_book_products = pd.DataFrame()

        df_order_book = df_order_book.drop(columns=['customer', 'paymentTerms', 'products'], axis=1)

        df_order_book = df_order_book.rename(columns={
            '_id' : 'order_book_uid',
            'orderBookId' : 'order_book_id',
            'purchaseOrderNumber' : 'purchase_order_number',
            'purchaseOrderDate' : 'purchase_order_date',
            'createdAt' : 'created_at',
            'createdBy' : 'created_by',
            'lastUpdatedAt' : 'updated_at',
            'lastUpdatedBy' : 'updated_by',
            'buyerCurrency' : 'buyer_currency',
            'category' : 'category',
            'orderType' : 'order_type',
            'poValue' : 'po_value',
            'taxPercent' : 'tax_percent',
            'countryOfDelivery' : 'country_of_delivery',
            'sampleRequestedBy' : 'sample_requested_by',
            'deliveryAddress' : 'delivery_address',
            'deliveryInstructions' : 'delivery_instructions',
            'testKey' : 'test_key',
            'inventoryId' : 'inventory_id'

        })
        df_order_book['purchase_order_number'] = df_order_book['purchase_order_number'].str.strip()

        df_order_book['created_at'] = pd.to_datetime(df_order_book['created_at'], utc=True)
        df_order_book['updated_at'] = pd.to_datetime(df_order_book['updated_at'], utc=True)
        df_order_book['purchase_order_date'] = pd.to_datetime(df_order_book['purchase_order_date'], utc=True)
        df_order_book['po_value'] = df_order_book.apply(
                    lambda x: self.convert_to_usd_value(x['po_value'], x['buyer_currency']),
                    axis=1
        )
        df_order_book['country_of_delivery'] = df_order_book['country_of_delivery'].str.lower()
        df_order_book = df_order_book.astype({
            'order_book_uid' : 'string',
            'order_book_id' : 'string',
            'purchase_order_number' : 'string',
            'created_by' : 'string',
            'updated_by' : 'string',
            'customer_id' : 'string',
            'credit_amount' : 'float64',
            'start_date' : 'string',
            'buyer_currency' : 'string',
            'creditor_days' : 'float64',
            'category' : 'string',
            'order_type' : 'string',
            'po_value' : 'float64',
            'tax_percent' : 'float64',
            'country_of_delivery' : 'string',
            'sample_requested_by' : 'string',
            'delivery_address' : 'string',
            'delivery_instructions' : 'string',
            'test_key' : 'bool',
            'inventory_id' : 'string'
        })
        
        df_order_book_products.drop(columns=['buyer_currency'], axis=1, inplace=True)
              
        return df_order_book, df_order_book_products
    
    def get_packaging(self, last_updated_timestamp):
        '''
        This function is used to get the packaging data from the mongo db and return it as a dataframe.
        '''
        logging.info(f"Getting packaging data from mongo db after {last_updated_timestamp}")
        cols_to_query = {'_id':1, 'type':1, 'packSize':1, 'tareWeight':1,'UOM':1,'packagingName':1,'pSize':1, 'pUom':1,  'tWeight':1,  'tWeightUom':1,
                          'deleted':1, 'createdAt':1, 'createdBy':1, 'lastUpdatedAt':1, 'lastUpdatedBy':1}
        
        packaging_data = self.mg_ops.get_collection('packaging').find({
             "$or": [
                {"lastUpdatedAt": {"$gt": last_updated_timestamp}},
                {
                    "$and": [
                        {"lastUpdatedAt": None},
                        {"createdAt": {"$gt": last_updated_timestamp}}
                    ]
                }
            ]
        }, cols_to_query)
        df_packaging = pd.DataFrame(packaging_data)

        logging.info(f"Packaging data fetched successfully. Total records: {df_packaging.shape}")
        if df_packaging.empty:
            return pd.DataFrame()

        for cols, val in cols_to_query.items():
            if cols not in df_packaging.columns:
                df_packaging[cols] = None

        df_packaging = df_packaging.rename(columns={
            '_id' : 'packaging_uid',
            'type' : 'type',
            'packSize' : 'pack_size',
            'tareWeight' : 'tare_weight',
            'UOM' : 'uom',
            'packagingName' : 'packaging_name',
            'pSize' : 'p_size',
            'pUom' : 'p_uom',
            'tWeight' : 't_weight',
            'tWeightUom' : 't_weight_uom',
            'createdAt' : 'created_at',
            'createdBy' : 'created_by',
            'lastUpdatedAt' : 'updated_at',
            'lastUpdatedBy' : 'updated_by'
        })
        df_packaging['created_at'] = pd.to_datetime(df_packaging['created_at'], utc=True)
        df_packaging['updated_at'] = pd.to_datetime(df_packaging['updated_at'], utc=True)
        df_packaging = df_packaging.astype({
            'packaging_uid' : 'string',
            'type' : 'string',
            'pack_size' : 'string',
            'tare_weight' : 'string',
            'uom' : 'string',
            'packaging_name' : 'string',
            'p_size' : 'float64',
            'p_uom' : 'string',
            't_weight' : 'float64',
            't_weight_uom' : 'string',
            'created_by' : 'string',
            'updated_by' : 'string'
        })
        
        return df_packaging
    
    def get_product_category(self, categories):
        try:
            if isinstance(categories, list) and len(categories) > 0:
                return [categories[0].get('subCategory', None), categories[0].get('category', None)]
            return [None, None]
        except:
            return [None, None]
    
    def get_product(self, last_updated_timestamp):
        '''
        This function is used to get the product data from the mongo db and return it as a dataframe.
        '''
        cols_to_query = {'_id':1, 'productId':1, 'tradeName':1, 'grade':1, 'technicalName':1, 'categories':1, 'deleted':1, 'functions':1,'family':1,'casNumber':1,
                         'createdAt':1, 'createdBy':1, 'lastUpdatedAt':1, 'lastUpdatedBy':1}

        product_data = self.mg_ops.get_collection('product').find({
             "$or": [
                {"lastUpdatedAt": {"$gt": last_updated_timestamp}},
                {
                    "$and": [
                        {"lastUpdatedAt": None},
                        {"createdAt": {"$gt": last_updated_timestamp}}
                    ]
                }
            ]
        }, cols_to_query)
        df_product = pd.DataFrame(product_data)
        logging.info(f"Product data fetched successfully. Total records: {df_product.shape}")
        if df_product.empty:
            return pd.DataFrame()
        for cols, val in cols_to_query.items():
            if cols not in df_product.columns:
                df_product[cols] = None

        df_product[['sub_category', 'category']] = df_product.apply(lambda x : self.get_product_category(x['categories']), axis=1, result_type='expand')
        df_product['functions'] = df_product['functions'].apply(lambda x: ','.join(map(str, x)) if isinstance(x, list) else None)
        df_product['family'] = df_product['family'].apply(lambda x: ','.join(map(str, x)) if isinstance(x, list) else None)


        df_product = df_product.drop(columns=['categories'], axis=1)

        df_product = df_product.rename(columns={
            '_id' : 'product_uid',
            'productId' : 'product_id',
            'tradeName' : 'trade_name',
            'grade' : 'grade',
            'technicalName' : 'technical_name',
            'functions' : 'functions',
            'family' : 'family',
            'casNumber' : 'cas_number',
            'createdAt' : 'created_at',
            'createdBy' : 'created_by',
            'lastUpdatedAt' : 'updated_at',
            'lastUpdatedBy' : 'updated_by'
        })
        df_product['created_at'] = pd.to_datetime(df_product['created_at'], utc=True)
        df_product['updated_at'] = pd.to_datetime(df_product['updated_at'], utc=True)
        df_product = df_product.astype({
            'product_uid' : 'string',
            'product_id' : 'string',
            'trade_name' : 'string',
            'grade' : 'string',
            'technical_name' : 'string',
            'functions' : 'string',
            'family' : 'string',
            'cas_number' : 'string',
            'created_by' : 'string',
            'updated_by' : 'string',
            'sub_category' : 'string',
            'category' : 'string',
            'deleted' : 'bool'

        })
        
        return df_product
    

    def get_product_batch(self, last_updated_timestamp):
        '''
        This function is used to get the product batch data from the mongo db and return it as a dataframe.
        '''
        cols_to_query = {'_id':1, 'units':1, 'supplierOrderDispatchId':1, 'customerOrderDispatchId':1, 'netWeightPerUnit':1, 'packagingId':1, 'unitOfMeasure':1, 'productId':1, 'type':1, 'deleted':1,
        'createdAt':1, 'createdBy':1,  'lastUpdatedAt':1, 'lastUpdatedBy':1}

        product_batch_data = self.mg_ops.get_collection('productBatchDetail').find({
             "$or": [
                {"lastUpdatedAt": {"$gt": last_updated_timestamp}},
                {
                    "$and": [
                        {"lastUpdatedAt": None},
                        {"createdAt": {"$gt": last_updated_timestamp}}
                    ]
                }
            ]
        }, cols_to_query)

        df_product_batch = pd.DataFrame(product_batch_data)
        logging.info(f"Product batch data fetched successfully. Total records: {df_product_batch.shape}")

        if df_product_batch.empty:
            return pd.DataFrame()
        for cols, val in cols_to_query.items():
            if cols not in df_product_batch.columns:
                df_product_batch[cols] = None

        df_product_batch = df_product_batch.rename(columns={
            '_id' : 'product_batch_uid',
            'units' : 'units',
            'supplierOrderDispatchId' : 'supplier_order_dispatch_id',
            'customerOrderDispatchId' : 'customer_order_dispatch_id',
            'netWeightPerUnit' : 'net_weight_per_unit',
            'packagingId' : 'packaging_id',
            'unitOfMeasure' : 'unit_of_measure',
            'productId' : 'product_id',
            'type' : 'type',
            'createdAt' : 'created_at',
            'createdBy' : 'created_by',
            'lastUpdatedAt' : 'updated_at',
            'lastUpdatedBy' : 'updated_by'
        })
        df_product_batch['created_at'] = pd.to_datetime(df_product_batch['created_at'], utc=True)
        df_product_batch['updated_at'] = pd.to_datetime(df_product_batch['updated_at'], utc=True)
        df_product_batch = df_product_batch.astype({
            'product_batch_uid' : 'string',
            'units' : 'int64',
            'supplier_order_dispatch_id' : 'string',
            'customer_order_dispatch_id' : 'string',
            'net_weight_per_unit' : 'float64',
            'packaging_id' : 'string',
            'unit_of_measure' : 'string',
            'product_id' : 'string',
            'type' : 'string',
            'created_by' : 'string',
            'updated_by' : 'string'
        })
        
        return df_product_batch
    
    def get_supplier_products(self, products_df : pd.DataFrame):
        # Create separate product records for each supplier
        
        products_df = products_df.rename(columns={'_id':'supplier_id'})
        df_supplier_products = products_df.explode('products').reset_index(drop=True)
        if df_supplier_products.empty:
            return pd.DataFrame()

        df_supplier_products_info = pd.json_normalize(df_supplier_products['products'])
        df_supplier_products = pd.concat([df_supplier_products.drop(columns=['products'], axis=1), df_supplier_products_info], axis=1)
        found = False
        for col in df_supplier_products.columns:
            if 'product.' in col:
                found = True
                break

        if df_supplier_products.empty or not found:
            return pd.DataFrame()
        if '_id' not in df_supplier_products.columns:
            df_supplier_products['_id'] = df_supplier_products['supplier_id'].astype(str) + '_' + df_supplier_products['product._id'].astype(str)
            
        df_supplier_products = df_supplier_products.rename(columns={'product._id':'product_id'})
        for address in ['address.street', 'address.city', 'address.state','address.postalCode', 'address.country']:
            if address not in df_supplier_products.columns:
                df_supplier_products[address] = None

        
        cols_to_drop = ['product','mstackDocuments','address','documents','packaging','supplier_product_uid']
        for col in cols_to_drop:
            if col in df_supplier_products.columns:
                df_supplier_products = df_supplier_products.drop(columns=[col], axis=1)
        
        cols_required= ['capacityAvailable','totalCapacity','leadTime','typicalOrderSize','hazardous','hsCode']
        for col in cols_required:
            if col not in df_supplier_products.columns:
                df_supplier_products[col] = None
        df_supplier_products = df_supplier_products.rename(columns={
            'capacityAvailable' : 'capacity_available',
            'totalCapacity' : 'total_capacity',
            'leadTime' : 'lead_time',
            'typicalOrderSize' : 'typical_order_size',
            'hazardous' : 'hazardous',
            'address.street' : 'street',
            'address.city' : 'city',
            'address.state' : 'state',
            'address.postalCode' : 'postal_code',
            'address.country' : 'country',
            '_id' : 'supplier_product_uid',
            'hsCode' : 'hs_code'
        })
        df_supplier_products = df_supplier_products.astype({
            'capacity_available' : 'float64',
            'total_capacity' : 'float64',
            'typical_order_size' : 'float64',
            'hazardous' : 'bool',
            'product_id' : 'string',
            'street' : 'string',
            'city' : 'string',
            'state' : 'string',
            'postal_code' : 'string',
            'country' : 'string',
            'supplier_product_uid' : 'string',
            'hs_code' : 'string',
            'supplier_id' : 'string',
        })
        for col in ['product.']:
            for cols in df_supplier_products.columns:
                if col in cols:
                    df_supplier_products = df_supplier_products.drop(columns=[cols], axis=1)

        cols_to_remove = ['certificateDocuments','hazardousLevel']
        for col in cols_to_remove:
            if col in df_supplier_products.columns:
                df_supplier_products = df_supplier_products.drop(columns=[col], axis=1)
        df_supplier_products = df_supplier_products.dropna(subset=['supplier_product_uid'], how='all')
        return df_supplier_products
    
    def get_supplier(self, last_updated_timestamp):
        '''
        This function is used to get the supplier data from the mongo db and return it as a dataframe.
        '''
        cols_to_query = {'_id':1, 'supplierId':1, 'name':1, 'address':1, 'email':1, 'mobile':1, 'products':1, 'totalCapacity':1, 'revenue':1,
                          'deleted':1, 'createdBy':1, 'lastUpdatedBy':1, 'createdAt':1, 'lastUpdatedAt':1}
        
        supplier_data = self.mg_ops.get_collection('supplier').find({
            "$or": [
                {"lastUpdatedAt": {"$gt": last_updated_timestamp}},
                {
                    "$and": [
                        {"lastUpdatedAt": None},
                        {"createdAt": {"$gt": last_updated_timestamp}}
                    ]
                }
            ]
        }, cols_to_query)

        df_supplier = pd.DataFrame(supplier_data)
        logging.info(f"Supplier data fetched successfully. Total records: {df_supplier.shape}")
        if df_supplier.empty:
            return pd.DataFrame(), pd.DataFrame()
        for cols, val in cols_to_query.items():
            if cols not in df_supplier.columns:
                df_supplier[cols] = None

        df_supplier[['street','state', 'country','postal_code','city']] = df_supplier.apply(lambda x : self.get_customer_address(x['address']), axis=1, result_type='expand')

        df_supplier_products = self.get_supplier_products(df_supplier[['products', '_id']])

        cols_to_drop = ['address','products']
        for col in cols_to_drop:
            if col in df_supplier.columns:
                df_supplier = df_supplier.drop(columns=[col], axis=1)

        df_supplier = df_supplier.rename(columns={
            '_id' : 'supplier_uid',
            'supplierId' : 'supplier_id',
            'name' : 'supplier_name',
            'totalCapacity' : 'total_capacity',
            'revenue' : 'revenue',
            'createdAt' : 'created_at',
            'createdBy' : 'created_by',
            'lastUpdatedAt' : 'updated_at',
            'lastUpdatedBy' : 'updated_by'
        })

        df_supplier['created_at'] = pd.to_datetime(df_supplier['created_at'], utc=True)
        df_supplier['updated_at'] = pd.to_datetime(df_supplier['updated_at'], utc=True)
        df_supplier = df_supplier.astype({
            'supplier_uid' : 'string',
            'supplier_id' : 'string',
            'supplier_name' : 'string',
            'created_by' : 'string',
            'updated_by' : 'string',
            'email' : 'string',
            'mobile' : 'string',
            'street' : 'string',
            'state' : 'string',
            'country' : 'string',
            'postal_code' : 'string',
            'city' : 'string',
            'total_capacity' : 'float64',
            'revenue' : 'float64'
        })

        supplier_order_products_cols = [
            'supplier_product_uid',
            'capacity_available',
            'total_capacity', 
            'lead_time',
            'typical_order_size',
            'hazardous',
            'product_id',
            'street',
            'city',
            'postal_code', 
            'country',
            'hs_code',
            'state',
            'supplier_id'
        ]
        for col in supplier_order_products_cols:
            if col not in df_supplier_products.columns:
                df_supplier_products[col] = None
        df_supplier_products = df_supplier_products[supplier_order_products_cols]

        supplier_order_cols = [
            'supplier_uid',
            'supplier_id', 
            'supplier_name',
            'email',
            'mobile',
            'deleted',
            'created_by',
            'updated_by', 
            'created_at',
            'updated_at',
            'total_capacity',
            'revenue',
            'street',
            'state',
            'country',
            'postal_code',
            'city'
        ]
        for col in supplier_order_cols:
            if col not in df_supplier.columns:
                df_supplier[col] = None
        df_supplier = df_supplier[supplier_order_cols]
        return df_supplier, df_supplier_products
    
    def get_supplier_order_packaging(self, packaging_df : pd.DataFrame):
        # Create separate product records for each supplier order
        
        df_supplier_order_packaging = packaging_df.explode('packaging').reset_index(drop=True)
        if df_supplier_order_packaging.empty:
            return pd.DataFrame()
        df_supplier_order_packaging['packaging_id'] = df_supplier_order_packaging['packaging'].apply(lambda x: x.get('_id') if pd.notna(x) else None)
        cols_to_select = ['product_id','supplier_id','packaging_id']
        for col in cols_to_select:
            if col not in df_supplier_order_packaging.columns:
                df_supplier_order_packaging[col] = None
        df_supplier_order_packaging = df_supplier_order_packaging.astype({
            'product_id' : 'string',
            'supplier_id' : 'string',
            'packaging_id' : 'string'
        })
        df_supplier_order_packaging = df_supplier_order_packaging[cols_to_select]
        return df_supplier_order_packaging
    
    def get_supplier_order_products(self, products_df : pd.DataFrame):
        # Create separate product records for each supplier order
        df_supplier_order_products = products_df.explode('products').reset_index(drop=True)
        if df_supplier_order_products.empty:
            return pd.DataFrame()
        
        df_info = pd.json_normalize(df_supplier_order_products['products'])
        df_supplier_order_products = pd.concat([df_supplier_order_products.drop(columns=['products'], axis=1), df_info], axis=1)
        found = False
        for col in df_supplier_order_products.columns:
            if 'product.' in col:
                found = True
                break

        if df_supplier_order_products.empty or not found:
            return pd.DataFrame()
        df_supplier_order_products = df_supplier_order_products.rename(columns={'product._id':'product_id','packaging._id':'packaging_id'})
        cols_to_be_present = ['quantityPerUnit','perUnitWeight','price','hsCode','units','status','quantity','uom','chemstackPrice','perUnitKgValue', 'packaging.otherPackagingDetails']
        for col in cols_to_be_present:
            if col not in df_supplier_order_products.columns:
                df_supplier_order_products[col] = None
        
        cols_to_drop = ['product','mstackDocuments','address','documents','packaging','linkedOrders','remarks','batchDateVisible']
        

        df_supplier_order_products = df_supplier_order_products.rename(columns={
            '_id' : 'supplier_order_product_uid',
            'uom' : 'unit_of_measure',
            'quantityPerUnit' : 'quantity_per_unit',
            'perUnitWeight' : 'per_unit_weight',
            'hsCode' : 'hs_code',
            'units' : 'units',
            'status' : 'status',
            'quantity' : 'quantity',
            'price' : 'price',
            'chemstackPrice' : 'chemstack_price',
            'perUnitKgValue' : 'per_unit_kg_value',
            'packaging.otherPackagingDetails' : 'other_packaging_details'
        })
        for col in ['product.','packaging.']:
            for cols in df_supplier_order_products.columns:
                if col in cols:
                    df_supplier_order_products = df_supplier_order_products.drop(columns=[cols], axis=1)
        
        df_supplier_order_products = df_supplier_order_products.astype({
            'supplier_order_uid' : 'string',
            'supplier_order_product_uid' : 'string',
            'product_id' : 'string',
            'unit_of_measure' : 'string',
            'quantity_per_unit' : 'float64',
            'per_unit_weight' : 'float64',
            'hs_code' : 'string',
            'units' : 'int64',
            'status' : 'string',
            'quantity' : 'float64',
            'price' : 'float64',
            'supplier_id' : 'string',
            'supplier_order_uid' : 'string',
            'packaging_id' : 'string',
            'chemstack_price' : 'float64',
            'per_unit_kg_value' : 'float64',
            'other_packaging_details' : 'string'
        })
        for col in cols_to_drop:
            if col in df_supplier_order_products.columns:
                df_supplier_order_products = df_supplier_order_products.drop(columns=[col], axis=1)
        
        cols_to_required = [
            'supplier_id',
            'supplier_order_product_uid',
            'supplier_order_uid',
            'unit_of_measure',
            'quantity',
            'price', 
            'hs_code',
            'units',
            'status',
            'quantity_per_unit',
            'per_unit_weight',
            'product_id',
            'packaging_id',
            'chemstack_price',
            'per_unit_kg_value',
            'other_packaging_details'
        ]
        for col in cols_to_required:
            if col not in df_supplier_order_products.columns:
                df_supplier_order_products[col] = None
        df_supplier_order_products = df_supplier_order_products[cols_to_required]
        return df_supplier_order_products
    
    def get_supplier_product(self, products_df : pd.DataFrame):
        
        df_supplier_products = products_df

        df_supplier_products['address'] = df_supplier_products['supplier'].apply(lambda x: x.get('address') if isinstance(x, dict) else None)
        df_supplier_products['products'] = df_supplier_products['supplier'].apply(lambda x: x.get('products') if isinstance(x, dict) else None)
        df_supplier_products['supplier_id'] = df_supplier_products['supplier'].apply(lambda x : x.get('_id') if isinstance(x, dict) else None)
        df_supplier_products = df_supplier_products.explode('products').reset_index(drop=True)
        df_supplier_products_info = pd.json_normalize(df_supplier_products['products'])
        df_supplier_products = pd.concat([df_supplier_products.drop(columns=['products'], axis=1), df_supplier_products_info], axis=1)
        
        found = False
        for col in df_supplier_products.columns:
            if 'product.' in col:
                found = True
                break

        if df_supplier_products.empty or not found:
            return pd.DataFrame(), pd.DataFrame()
        

        df_supplier_products = df_supplier_products.rename(columns={'product._id':'product_id'})
        for col in ['product.']:
            for cols in df_supplier_products.columns:
                if col in cols:
                    df_supplier_products = df_supplier_products.drop(columns=[cols], axis=1)
        
        for address in ['address.street', 'address.city', 'address.state','address.postalCode', 'address.country']:
            if address not in df_supplier_products.columns:
                df_supplier_products[address] = None

        cols_to_present = ['capacityAvailable','totalCapacity','leadTime','typicalOrderSize','hazardous','totalCapacity','revenue','hsCode','packaging']
        for col in cols_to_present:
            if col not in df_supplier_products.columns:
                df_supplier_products[col] = None
        
        df_supplier_products['product_id'] = df_supplier_products['product_id'].apply(lambda x: x if pd.notna(x) else 'TEST')

        df_supplier_products = df_supplier_products.rename(columns={
            'capacityAvailable' : 'capacity_available',
            'totalCapacity' : 'total_capacity',
            'leadTime' : 'lead_time',
            'typicalOrderSize' : 'typical_order_size',
            'hazardous' : 'hazardous',
            'product_id' : 'product_id',
            'hsCode' : 'hs_code',
            'address.street' : 'street',
            'address.city' : 'city',
            'address.state' : 'state',
            'address.postalCode' : 'postal_code',
            'address.country' : 'country'
        })
        # Fill non-finite values in 'lead_time' with a default value (e.g., 0) before casting
        df_supplier_products['lead_time'] = df_supplier_products['lead_time'].fillna(0).replace([float('inf'), -float('inf')], 0)
        

        df_supplier_products = df_supplier_products.astype({
            'capacity_available' : 'float64',
            'total_capacity' : 'float64',
            'lead_time' : 'int64',
            'typical_order_size' : 'float64',
            'hazardous' : 'bool',
            'product_id' : 'string',
            'supplier_id' : 'string',
            'street' : 'string',
            'city' : 'string',
            'state' : 'string',
            'postal_code' : 'string',
            'country' : 'string',
            'supplier_order_uid' : 'string',
            'hs_code' : 'string',
            'revenue' : 'float64'
        })
        
        
        df_supplier_product_packaging = self.get_supplier_order_packaging(df_supplier_products[['packaging','product_id','supplier_id']])
        
        cols_to_drop = ['address','products','supplier','certificateDocuments','hazardousLevel','mstackDocuments','documents','packaging']
        for col in cols_to_drop:
            if col in df_supplier_products.columns:
                df_supplier_products = df_supplier_products.drop(columns=[col], axis=1)
        required_columns = [
            'supplier_order_uid',
            'supplier_id',
            'capacity_available', 
            'total_capacity',
            'lead_time',
            'typical_order_size',
            'hazardous',
            'product_id',
            'street',
            'city', 
            'state',
            'postal_code',
            'country',
            'hs_code',
            'revenue'
        ]

        for col in required_columns:
            if col not in df_supplier_products.columns:
                df_supplier_products[col] = None

        df_supplier_products = df_supplier_products[required_columns]

        return df_supplier_products, df_supplier_product_packaging
    
    def get_supplier_order(self, last_updated_timestamp):
        """
        This function is used to get the supplier order data from the mongo db and return it as a dataframe.
        """
        cols_to_query = {'_id':1, 'orderId':1, 'supplier':1, 'purchaseOrderNumber':1, 'purchaseOrderDate':1, 'products':1, 'incoterms':1, 'paymentTerms':1,'status':1,'perUnitWeight':1,'approved' : 1,'linkedSupplierOrderBookId':1,
                         'actualDateOfDeparture' : 1,'etaForDestination' : 1,'destinationTrackingNumber' : 1,
                         'sentForTestingOn' : 1, 'isfCutoffDate' : 1, 'isfActualDate' : 1, 'testingDate' : 1, 'eta' : 1,
                         'deleted':1, 'createdAt':1, 'createdBy':1, 'lastUpdatedAt':1, 'lastUpdatedBy':1,'route':1,'supplierDispatchedOn':1,'arrivalDateAtOrigin':1, 'dispatchToDestinationDate':1}
        
        supplier_order_data = self.mg_ops.get_collection('supplierOrder').find({
            # 'lastUpdatedAt' : {'$gt' : last_updated_timstamp}
                    "$or": [
                {"lastUpdatedAt": {"$gt": last_updated_timestamp}},
                {
                    "$and": [
                        {"lastUpdatedAt": None},
                        {"createdAt": {"$gt": last_updated_timestamp}}
                    ]
                }
            ]
        }, cols_to_query)
        df_supplier_order = pd.DataFrame(supplier_order_data)
        logging.info(f"Supplier order data fetched successfully. Total records: {df_supplier_order.shape}")
        if df_supplier_order.empty:
            return pd.DataFrame(), pd.DataFrame(), pd.DataFrame(), pd.DataFrame()
        for cols, val in cols_to_query.items():
            if cols not in df_supplier_order.columns:
                df_supplier_order[cols] = None

        df_supplier_order['supplier_id'] = df_supplier_order['supplier'].apply(lambda x : x['_id'])

        

        df_supplier_order = df_supplier_order.rename(columns={
            '_id' : 'supplier_order_uid',
            'orderId' : 'order_id',
            'purchaseOrderNumber' : 'purchase_order_number',
            'purchaseOrderDate' : 'purchase_order_date',
            'createdAt' : 'created_at',
            'createdBy' : 'created_by',
            'lastUpdatedAt' : 'updated_at',
            'lastUpdatedBy' : 'updated_by',
            'incoterms' : 'incoterms',
            'paymentTerms' : 'payment_terms',
            'approved' : 'approved',
            'perUnitWeight' : 'per_unit_weight',
            'actualDateOfDeparture' : 'actual_date_of_departure',
            'linkedSupplierOrderBookId' : 'linked_supplier_order_book_id',
            'route' : 'route',
            'supplierDispatchedOn' : 'supplier_dispatched_on',
            'arrivalDateAtOrigin' : 'arrival_date_at_origin',
            'dispatchToDestinationDate' : 'dispatch_to_destination_date',
            'etaForDestination' : 'eta_for_destination',
            'sentForTestingOn' : 'sent_for_testing_on',
            'isfCutoffDate' : 'isf_cutoff_date',
            'isfActualDate' : 'isf_actual_date',
            'destinationTrackingNumber' : 'destination_tracking_number',
            'testingDate' : 'testing_date'
        })
        df_supplier_order[['inco_type', 'inco_country', 'inco_shipment_method', 'inco_port']] = df_supplier_order.apply(lambda x : self.get_order_book_inco_terms(x['incoterms']), axis=1, result_type='expand')
        df_supplier_order_products = self.get_supplier_order_products(df_supplier_order[['products','supplier_id','supplier_order_uid']])

        if 'supplier' in df_supplier_order.columns:
            df_supplier_products, df_supplier_product_packaging = self.get_supplier_product(df_supplier_order[['supplier', 'supplier_order_uid']])
        else:
            df_supplier_products = pd.DataFrame()
            df_supplier_product_packaging = pd.DataFrame()

        df_supplier_order['created_at'] = pd.to_datetime(df_supplier_order['created_at'], utc=True)
        df_supplier_order['updated_at'] = pd.to_datetime(df_supplier_order['updated_at'], utc=True)
        df_supplier_order['actual_date_of_departure'] = pd.to_datetime(df_supplier_order['actual_date_of_departure'], utc=True)
        df_supplier_order['purchase_order_date'] = pd.to_datetime(df_supplier_order['purchase_order_date'], utc=True)
        df_supplier_order['supplier_dispatched_on'] = pd.to_datetime(df_supplier_order['supplier_dispatched_on'], utc=True)
        df_supplier_order['arrival_date_at_origin'] = pd.to_datetime(df_supplier_order['arrival_date_at_origin'], utc=True)
        df_supplier_order['dispatch_to_destination_date'] = pd.to_datetime(df_supplier_order['dispatch_to_destination_date'], utc=True)
        df_supplier_order['eta_for_destination'] = pd.to_datetime(df_supplier_order['eta_for_destination'], utc=True)
        df_supplier_order['sent_for_testing_on'] = pd.to_datetime(df_supplier_order['sent_for_testing_on'], utc=True)
        df_supplier_order['isf_cutoff_date'] = pd.to_datetime(df_supplier_order['isf_cutoff_date'], utc=True)
        df_supplier_order['isf_actual_date'] = pd.to_datetime(df_supplier_order['isf_actual_date'], utc=True)
        df_supplier_order['testing_date'] = pd.to_datetime(df_supplier_order['testing_date'], utc=True)
        df_supplier_order['eta'] = pd.to_datetime(df_supplier_order['eta'], utc=True)

        df_supplier_order = df_supplier_order.astype({
            'supplier_order_uid' : 'string',
            'created_by' : 'string',
            'updated_by' : 'string',
            'supplier_id' : 'string',
            'order_id' : 'string',
            'purchase_order_number' : 'string',
            'payment_terms' : 'string',
            'approved' : 'bool',
            'linked_supplier_order_book_id' : 'string',
            'status' : 'string',
            'per_unit_weight' : 'float64',
            'deleted' : 'bool',
            'inco_type' : 'string',
            'inco_country' : 'string',
            'inco_shipment_method' : 'string',
            'inco_port' : 'string',
            'route' : 'string',
            'destination_tracking_number' : 'string'

        })
        for cols in ['products','supplier','incoterms','payment_terms']:
            if cols in df_supplier_order.columns:
                df_supplier_order = df_supplier_order.drop(columns=[cols], axis=1)
        
        return df_supplier_order, df_supplier_order_products, df_supplier_products, df_supplier_product_packaging
    
    def get_supplier_order_book_products(self, products_df : pd.DataFrame):
        # Create separate product records for each supplier order
        products_df = products_df.rename(columns={'_id':'supplier_order_book_uid'})
        df_supplier_order_book_products = products_df.explode('products').reset_index(drop=True)
        df_supplier_order_book_products_info = pd.json_normalize(df_supplier_order_book_products['products'])

        df_supplier_order_book_products = pd.concat([df_supplier_order_book_products.drop(columns=['products'], axis=1), df_supplier_order_book_products_info], axis=1)

        found = False
        for col in df_supplier_order_book_products.columns:
            if 'product.' in col:
                found = True
                break

        if df_supplier_order_book_products.empty or not found:
            return pd.DataFrame()
        
        cols_to_present = ['_id','uom','quantity','price','product','incoterms','packaging','hsCode','margin','productDescription','label','units','incoterms.type', 'incoterms.country','incoterms.data.portOfLoading','incoterms.data.shipmentMethod',
       'incoterms.data.placeOfDelivery', 'incoterms.data.city','testingRequired','dispatchWithResults', 'packaging.otherPackagingDetails']
        for col in cols_to_present:
            if col not in df_supplier_order_book_products.columns:
                df_supplier_order_book_products[col] = None

        df_supplier_order_book_products = df_supplier_order_book_products.rename(columns={'product._id':'product_id','packaging._id':'packing_id', 'packaging.otherPackagingDetails' : 'other_packaging_details'})
        for col in ['product.','packaging.']:
            for cols in df_supplier_order_book_products.columns:
                if col in cols:
                    df_supplier_order_book_products = df_supplier_order_book_products.drop(columns=[cols], axis=1)
        
        
        if df_supplier_order_book_products.empty:
            return pd.DataFrame()
        

        

        cols_to_drop = [
       'incoterms.data.placeOfPickup', 
       'incoterms.data.placeOfDelivery', 
       'incoterms.data.portOfDischarge',
       'meta.assignedQuantity', 'incoterms.data.city','productDescription','remarks',
       'expectedDeliveryDate',
       'deliveryDate', 'documents.TDS', 'documents.SDS', 'quantityperunit', 'documents.COA']
        for col in cols_to_drop:
            if col in df_supplier_order_book_products.columns:
                df_supplier_order_book_products = df_supplier_order_book_products.drop(columns=[col], axis=1)

        df_supplier_order_book_products = df_supplier_order_book_products.rename(columns={
            '_id': 'supplier_order_book_product_id',
            'uom': 'unit_of_measure',
            'label' : 'label',
            'units' : 'units',
            'hsCode' : 'hs_code',
            'margin' : 'margin',
            'incoterms.type' : 'inco_type',
            'incoterms.country' : 'inco_country',
            'incoterms.data.portOfLoading' : 'inco_port',
            'incoterms.data.shipmentMethod' : 'inco_shipment_method',
            'dispatchWithResults' : 'dispatch_with_results',
            'testingRequired' : 'testing_required',

        })

        df_supplier_order_book_products = df_supplier_order_book_products.astype({
            'supplier_order_book_uid' : 'string',
            'supplier_order_book_product_id' : 'string',
            'product_id' : 'string',
            'packing_id' : 'string',
            'inco_type' : 'string',
            'inco_country' : 'string',
            'inco_shipment_method' : 'string',
            'inco_port' : 'string',
            'hs_code' : 'string',
            'margin' : 'float64',
            'label' : 'string',
            'unit_of_measure' : 'string',
            'price' : 'float64',
            'quantity' : 'float64',
            'dispatch_with_results' : 'bool',
            'testing_required' : 'bool',
            'other_packaging_details' : 'string'
        })

        cols_to_drop = ['product','incoterms','packaging','productDescription','products']
        for col in cols_to_drop:
            if col in df_supplier_order_book_products.columns:
                df_supplier_order_book_products = df_supplier_order_book_products.drop(columns=[col], axis=1)
        
        return df_supplier_order_book_products
    
    def get_activity_remarks(self, activity_df: pd.DataFrame) -> pd.DataFrame:
        """
        Process remarks received and given for activities.
        Returns a dataframe with activity_id, task_id, task_name, remark and remark_type.
        """
        remarks_list = []
        
        for _, row in activity_df.iterrows():
            activity_id = row.get('activity_id')
            
            for remark_type, column in [('RECEIVED', 'remarksReceived'), ('GIVEN', 'remarksGiven')]:
                remarks = row.get(column)
                if isinstance(remarks, list):
                    remarks_list.extend({
                        'activity_id': activity_id,
                        'task_id': r.get('taskId'),
                        'task_name': r.get('taskName'),
                        'remark': r.get('remark'),
                        'remark_type': remark_type
                    } for r in remarks)

        if not remarks_list:
            return pd.DataFrame(columns=['activity_id', 'task_id', 'task_name', 'remark', 'remark_type'])

        df_remarks = pd.DataFrame(remarks_list).astype({
            'activity_id': 'string',
            'task_id': 'string',
            'task_name': 'string',
            'remark': 'string',
            'remark_type': 'string'
        })

        return df_remarks
    
    def get_activity_log(self, last_updated_timestamp):
        """
        This function is used to get the activity log data from the mongo db and return it as a dataframe.
        """
        cols_to_query = {'_id':1, 'entityId':1, 'entityType':1, 'orderId':1, 'secondaryId':1, 'name':1, 'description':1, 
                 'deleted':1, 'category':1, 'status':1, 'createdBy':1, 'createdAt':1, 'group':1, 
                 'taskId':1, 'dependentOn':1, 'dependencyResolved':1, 'orderType':1, 'onHold':1, 
                 'customerName':1, 'productName':1, 'assignedTo':1, 'dueDate':1, 'dueDateLogic':1,'taskCompletedOn' : 1,
                 'lastUpdatedAt' : 1, 'remarksReceived' : 1, 'remarksGiven':1,
                 }

        activity_log_data = self.mg_ops.get_collection('activity').find({
            # 'lastUpdatedAt' : {'$gt' : last_updated_timstamp}
                    "$or": [
                {"lastUpdatedAt": {"$gt": last_updated_timestamp}},
                {
                    "$and": [
                        {"lastUpdatedAt": None},
                        {"createdAt": {"$gt": last_updated_timestamp}}
                    ]
                }
            ]
        }, cols_to_query)

        df_activity = pd.DataFrame(activity_log_data)

        logging.info(f"Activity log data fetched successfully. Total records: {df_activity.shape}")
        if df_activity.empty:
            return pd.DataFrame(), pd.DataFrame()

        for cols, val in cols_to_query.items():
            if cols not in df_activity.columns:
                df_activity[cols] = None

        df_activity = df_activity.rename(columns={
            '_id': 'activity_id',
            'entityId': 'entity_id', 
            'entityType': 'entity_type',
            'orderId': 'order_id',
            'secondaryId': 'secondary_id',
            'createdAt': 'created_at',
            'createdBy': 'created_by',
            'taskId': 'task_id',
            'dependentOn': 'dependent_on',
            'dependencyResolved': 'dependency_resolved',
            'orderType': 'order_type',
            'onHold': 'on_hold',
            'customerName': 'customer_name',
            'productName': 'product_name',
            'assignedTo': 'assigned_to',
            'dueDate': 'due_date',
            'dueDateLogic': 'due_date_logic',
            'taskCompletedOn' : 'task_completed_on',
            'lastUpdatedAt' : 'last_updated_at'
            
        })
        df_activity_remarks = self.get_activity_remarks(df_activity)
        df_activity['created_at'] = pd.to_datetime(df_activity['created_at'], utc=True)
        df_activity['due_date'] = pd.to_datetime(df_activity['due_date'], utc=True)
        df_activity['task_completed_on'] = pd.to_datetime(df_activity['task_completed_on'], utc=True)
        df_activity['last_updated_at'] = pd.to_datetime(df_activity['last_updated_at'], utc=True)

        if 'task_id' not in df_activity.columns:
            df_activity['task_id'] = 0  # Add task_id column with default value 0 if it doesn't exist
        else:
            df_activity['task_id'] = df_activity['task_id'].fillna(0).astype(int)
            # Fill missing values in task_id with 0 and ensure it's of type int


        df_activity = df_activity.astype({
            'activity_id': 'string',
            'entity_id': 'string',
            'entity_type': 'string', 
            'order_id': 'string',
            'secondary_id': 'string',
            'name': 'string',
            'description': 'string',
            'deleted': 'bool',
            'category': 'string', 
            'status': 'string',
            'created_by': 'string',
            'group': 'string',
            'task_id': int,
            'dependent_on': 'string',
            'dependency_resolved': 'bool',
            'order_type': 'string',
            'on_hold': 'bool',
            'customer_name': 'string',
            'product_name': 'string',
            'assigned_to': 'string',
            'due_date_logic': 'string'
        })

        return df_activity, df_activity_remarks
    
    def get_supplier_order_book(self, last_updated_timestamp):
        cols_to_query = {'_id':1, 'orderBookId':1, 'supplier':1, 'purchaseOrderNumber':1, 'purchaseOrderDate':1, 'products':1,
                         'billingAddress':1,'shippingAddress':1, 'perUnitWeight':1,'currencyType':1,'supplierRefNumber':1,'modeOfDelivery':1,'deliveryTerm':1,'deliveryLocation':1,'deliverySchedule':1,
                         'linkedCOBId' : 1, 'mrd' : 1,'additionalCondition' : 1, 'pickupLocation' : 1,
                         'paymentTerms':1,'deleted':1, 'createdAt':1, 'createdBy':1, 'lastUpdatedAt':1, 'lastUpdatedBy':1}
        
        supplier_order_book_data = self.mg_ops.get_collection('supplierOrderBook').find({
            #'lastUpdatedAt' : {'$gt' : last_updated_timstamp}
             "$or": [
                {"lastUpdatedAt": {"$gt": last_updated_timestamp}},
                {
                    "$and": [
                        {"lastUpdatedAt": None},
                        {"createdAt": {"$gt": last_updated_timestamp}}
                    ]
                }
            ]
        }, cols_to_query)
        df_supplier_order_book = pd.DataFrame(supplier_order_book_data)

        logging.info(f"Supplier order book data fetched successfully. Total records: {df_supplier_order_book.shape}")
        if df_supplier_order_book.empty:
            return pd.DataFrame(), pd.DataFrame()
        for cols, val in cols_to_query.items():
            if cols not in df_supplier_order_book.columns:
                df_supplier_order_book[cols] = None
        df_supplier_order_book['supplier_id'] = df_supplier_order_book['supplier'].apply(lambda x : x['_id'])

        if 'products' in df_supplier_order_book.columns:
            df_supplier_order_book_products = self.get_supplier_order_book_products(df_supplier_order_book[['products', '_id']])
        else:
            df_supplier_order_book_products = pd.DataFrame()

        df_supplier_order_book = df_supplier_order_book.rename(columns={
            '_id' : 'supplier_order_book_uid',
            'orderBookId' : 'so_order_book_id',
            'purchaseOrderNumber' : 'purchase_order_number',
            'purchaseOrderDate' : 'purchase_order_date',
            'createdAt' : 'created_at',
            'createdBy' : 'created_by',
            'lastUpdatedAt' : 'updated_at',
            'lastUpdatedBy' : 'updated_by',
            'perUnitWeight' : 'per_unit_weight',
            'currencyType' : 'currency_type',
            'supplierRefNumber' : 'supplier_ref_number',
            'modeOfDelivery' : 'mode_of_delivery',
            'deliveryTerm' : 'delivery_term',
            'deliveryLocation' : 'delivery_location',
            'deliverySchedule' : 'delivery_schedule',
            'paymentTerms' : 'payment_terms',
            'billingAddress' : 'billing_address',
            'shippingAddress' : 'shipping_address',
            'linkedCOBId' : 'order_book_id',
            'additionalCondition' : 'additional_condition',
            'pickupLocation' : 'pickup_location'
        })  

        df_supplier_order_book['delivery_date'] = df_supplier_order_book['delivery_schedule'].apply(lambda x : x[0].get('deliveryDate') if isinstance(x, list) and len(x) > 0 else None)
        df_supplier_order_book['delivery_date'] = pd.to_datetime(df_supplier_order_book['delivery_date'], utc=True)
        df_supplier_order_book = df_supplier_order_book.drop(columns=['delivery_schedule'], axis=1)

        df_supplier_order_book['created_at'] = pd.to_datetime(df_supplier_order_book['created_at'], utc=True)
        df_supplier_order_book['updated_at'] = pd.to_datetime(df_supplier_order_book['updated_at'], utc=True)
        df_supplier_order_book['purchase_order_date'] = pd.to_datetime(df_supplier_order_book['purchase_order_date'], utc=True)
        df_supplier_order_book['mrd'] = pd.to_datetime(df_supplier_order_book['mrd'], utc=True)
        df_supplier_order_book[['creditor_days', 'credit_amount', 'advance_amount', 'po_payment_terms']] = df_supplier_order_book.apply(lambda x : self.get_so_order_book_payment_terms(x.get('payment_terms', {})), axis=1, result_type='expand')
        df_supplier_order_book = df_supplier_order_book.astype({
            'supplier_order_book_uid' : 'string',
            'created_by' : 'string',
            'updated_by' : 'string',
            'supplier_id' : 'string',
            'so_order_book_id' : 'string',
            'order_book_id' : 'string',
            'purchase_order_number' : 'string',
            'per_unit_weight' : 'float64',
            'currency_type' : 'string',
            'supplier_ref_number' : 'string',
            'mode_of_delivery' : 'string',
            'delivery_term' : 'string',
            'delivery_location' : 'string',
            'billing_address' : 'string',
            'shipping_address' : 'string',
            'deleted' : 'bool',
            'additional_condition' : 'string',
            'credit_amount' : 'float64',
            'advance_amount' : 'float64',
            'pickup_location' : 'string',
            'po_payment_terms' : 'string'
        })
        for col in ['products','supplier','payment_terms', 'documents.COA']:
            if col in df_supplier_order_book.columns:
                df_supplier_order_book = df_supplier_order_book.drop(columns=[col], axis=1)
        
        return df_supplier_order_book, df_supplier_order_book_products
    
    def get_users(self, last_updated_timestamp):
        '''
        This function is used to get the user data from the mongo db and return it as a dataframe.
        '''
        cols_to_query = {'_id':1, 'username':1, 'entityId':1, 'entityType':1, 'permissionsGroups':1, 'permissions':1, 'createdAt':1, 'isEmailVerified':1, 'updatedAt':1}
        user_data = self.mg_ops.get_collection('users').find(    {
            "$or": [
                {"updatedAt": {"$gt": last_updated_timestamp}},
                {
                    "$and": [
                        {"updatedAt": None},
                        {"createdAt": {"$gt": last_updated_timestamp}}
                    ]
                }
            ]
            
        }, cols_to_query)
    
        df_user = pd.DataFrame(user_data)
        logging.info(f"User data fetched successfully. Total records: {df_user.shape}")
        if df_user.empty:
            return pd.DataFrame()
        for cols, val in cols_to_query.items():
            if cols not in df_user.columns:
                df_user[cols] = None

        df_user = df_user.rename(columns={
            '_id' : 'user_uid',
            'username' : 'username',
            'entityId' : 'entity_id',
            'entityType' : 'entity_type',
            'permissionsGroups' : 'permissions_groups',
            'permissions' : 'permissions',
            'createdAt' : 'created_at',
            'isEmailVerified' : 'is_email_verified',
            'updatedAt' : 'updated_at'
        })

        df_user['permissions_groups'] = df_user['permissions_groups'].apply(lambda x : ','.join(x) if isinstance(x, list) else None)
        df_user['permissions'] = df_user['permissions'].apply(lambda x : ','.join(x) if isinstance(x, list) else None)

        df_user['created_at'] = pd.to_datetime(df_user['created_at'], utc=True)
        df_user['updated_at'] = pd.to_datetime(df_user['updated_at'], utc=True)

        df_user = df_user.astype({
            'user_uid' : 'string',
            'username' : 'string',
            'entity_id' : 'string',
            'entity_type' : 'string',
            'permissions_groups' : 'string',
            'permissions' : 'string',
            'is_email_verified' : 'boolean',
        })

        return df_user
    
    def get_operational_invoices(self, last_updated_timestamp):
        """
        Fetch operational invoices from MongoDB since the last updated timestamp and return as a cleaned DataFrame.
        """

        cols_to_query = {
            '_id': 1,
            'customerPurchcaseOrderId': 1,
            'customerOrderBookId': 1,
            'customerName': 1,
            'invoices': 1,
            'createdAt': 1,
            'lastUpdatedAt': 1
        }

        # MongoDB query
        cursor = self.mg_ops.get_collection('operationalInvoices').find({
            "$or": [
                {"lastUpdatedAt": {"$gt": last_updated_timestamp}},
                {
                    "$and": [
                        {"lastUpdatedAt": None},
                        {"createdAt": {"$gt": last_updated_timestamp}}
                    ]
                }
            ]
        }, cols_to_query)

        # Load into DataFrame
        df_invoices = pd.DataFrame(list(cursor))
        if df_invoices.empty:
            return pd.DataFrame()
        
        for col in cols_to_query:
            if col not in df_invoices.columns:
                df_invoices[col] = None

        # Base fields
        df_invoices["invoice_id"] = df_invoices["_id"].astype(str)
        df_invoices = df_invoices.drop(columns=["_id"])

        # Explode the invoices array
        df_invoices = df_invoices.explode("invoices").reset_index(drop=True)

        # Flatten nested invoices
        invoice_fields = pd.json_normalize(df_invoices["invoices"], sep=".")
        df_invoices = pd.concat([df_invoices.drop(columns=["invoices"]), invoice_fields], axis=1)

        # Ensure all expected fields exist
        expected_fields = [
            "invoice_id", "invoiceType", "invoiceNumber", "invoiceDate", "invoiceAmountWithoutTax",
            "currencyType", "dueDate", "taxAmount", "invoiceQuantity", "totalAmount", "allocatedAmount",
            "reasonforDeviation", "remarks", "conversionRate", "invoiceFile.fileId",
            "supplierName", "supplierOrderId", "chemicalName",
            "customerDispatchOrderId", "customCostDescription",
            "customerPurchaseOrderId", "customerOrderBookId",
            "customerName", "createdAt", "updatedAt"
        ]
        for col in expected_fields:
            if col not in df_invoices.columns:
                df_invoices[col] = None

        # Rename columns to match expected output
        df_invoices.rename(columns={
            "invoiceType": "invoice_type",
            "invoiceNumber": "invoice_number",
            "invoiceDate": "invoice_date",
            "invoiceAmountWithoutTax": "invoice_amount_without_tax",
            "currencyType": "currency_type",
            "dueDate": "due_date",
            "taxAmount": "tax_amount",
            "invoiceQuantity": "invoice_quantity",
            "totalAmount": "total_amount",
            "allocatedAmount": "allocated_amount",
            "reasonforDeviation": "reason_for_deviation",
            "remarks": "remarks",
            "conversionRate": "conversion_rate",
            "invoiceFile.fileId": "invoice_file_id",
            "supplierName": "supplier_name",
            "supplierOrderId": "supplier_order_id",
            "chemicalName": "chemical_name",
            "customCostDescription": "custom_cost_description",
            "customerDispatchOrderId": "customer_dispatch_order_id",
            "customerPurchcaseOrderId": "customer_purchase_order_id",
            "customerOrderBookId": "customer_order_book_id",
            "customerName": "customer_name",
            "createdAt": "created_at",
            "lastUpdatedAt": "updated_at"
        }, inplace=True)

        # Date parsing
        df_invoices["invoice_date"] = pd.to_datetime(df_invoices["invoice_date"], errors="coerce", utc=True)
        df_invoices["due_date"] = pd.to_datetime(df_invoices["due_date"], errors="coerce", utc=True)
        df_invoices["created_at"] = pd.to_datetime(df_invoices["created_at"], errors="coerce", utc=True)
        df_invoices["updated_at"] = pd.to_datetime(df_invoices["updated_at"], errors="coerce", utc=True)

        # Numeric casting
        for col in ["invoice_amount_without_tax", "tax_amount", "invoice_quantity", "total_amount","allocated_amount" ,"conversion_rate"]:
            df_invoices[col] = pd.to_numeric(df_invoices[col], errors="coerce")

        # Convert monetary amounts to USD
        amount_cols = ["invoice_amount_without_tax", "tax_amount", "total_amount", "allocated_amount"]

        for col in amount_cols:
            usd_col = f"{col}_usd"
            df_invoices[usd_col] = df_invoices.apply(
                lambda row: row[col] if row["currency_type"] == "USD" 
                else row[col] * row["conversion_rate"],
                axis=1
            )
        # String casting
        for col in [
            "invoice_id", "invoice_type", "invoice_number", "currency_type",
            "reason_for_deviation", "remarks", "invoice_file_id",
            "supplier_name", "supplier_order_id", "chemical_name",
            "customer_dispatch_order_id", "custom_cost_description",
            "customer_purchase_order_id", "customer_order_book_id", "customer_name"
        ]:
            df_invoices[col] = df_invoices[col].astype("string")

        logging.info(f"Operational invoices data fetched successfully. Total records: {df_invoices.shape}")
        return df_invoices
    
    def create_upsert_sql(self, df : pd.DataFrame, table_name, conflict_columns, schema='logistics'):
        if df.empty:
            return []
        
        
        # Get table columns
        main_table_name = table_name.split('.')[-1]
        col_query = f"SELECT column_name FROM information_schema.columns WHERE table_schema = '{schema}' AND table_name = '{main_table_name}';"
        table_cols = self.pg_ops.read_data(col_query)
        if not table_cols:
            raise Exception(f"Table {schema}.{table_name} not found")
        # Copy data to temp table
        columns = []
        for col in table_cols:
            col_name = col['column_name']
            if col_name in df.columns:
                columns.append(col_name)
            else:
                logging.warning(f"Column {col_name} not found in DataFrame, skipping.")

        # Handle both single column and multiple column conflicts 
        if isinstance(conflict_columns, tuple):
            conflict_cols = ','.join(conflict_columns)
        else:
            conflict_cols = conflict_columns

        # Fix list formatting in SQL
        columns_str = ','.join(columns)

        columns_list = list(columns_str.split(','))
        # Create temporary table
        temp_table = f"temp_{table_name.replace('.','_')}"
        drop_sql = f"DROP TABLE IF EXISTS {temp_table};"
        self.pg_ops.write_data(drop_sql)
        logging.info(f"Temporary table {temp_table} dropped successfully.")
        for col in df.columns:
            if col not in columns_list:
                df = df.drop(columns=[col], axis=1)
        df.to_sql(temp_table, self.pg_ops.engine, schema=schema, if_exists='replace', index=False)
        logging.info(f"Temporary table {temp_table} created successfully.")

        # Perform upsert from temp table
        upsert_sql = f"""
        INSERT INTO {table_name} ({columns_str})
        SELECT DISTINCT ON ({conflict_cols}) {columns_str}
        FROM {schema}.{temp_table}
        ON CONFLICT ({conflict_cols})
        DO UPDATE SET
            {', '.join([f"{col} = EXCLUDED.{col}" for col in columns])};
        """
        logging.info(f"Upsert SQL created successfully.")
        
        # Clean up
        drop_sql = f"DROP TABLE IF EXISTS {schema}.{temp_table};"
        
        return [upsert_sql, drop_sql]
    
    def create_all_tables(self):
        sql_paths = ['dags/sql/last_updated_timestamp.sql' ,'dags/sql/logistics/packaging.sql' ,'dags/sql/logistics/product.sql','dags/sql/logistics/customers.sql','dags/sql/logistics/employee.sql','dags/sql/logistics/order_book.sql','dags/sql/logistics/supplier.sql', 'dags/sql/logistics/supplier_order_book.sql',
                     'dags/sql/logistics/supplier_order.sql','dags/sql/logistics/users.sql','dags/sql/logistics/inventory.sql', 'dags/sql/logistics/operational_invoices.sql']
        for sql_path in sql_paths:
            logging.info(f"Creating table from {sql_path}")
            with open(airflow_home_path +"/dags/repo/" + sql_path, 'r') as file:
                sql_script = file.read()
                logging.info(f"Executing SQL script: {sql_script}")
                self.pg_ops.write_data(sql_script)
                logging.info(f"Table created from {sql_path}")

        logging.info("All tables created successfully.")
    
    def insert_metadata(self, table_name, last_updated):
        sql = f"""
        INSERT INTO common.etl_metadata (table_name, last_updated) 
        VALUES ('{table_name}', '{last_updated}')
        """
        self.pg_ops.write_data(sql)
    
    def get_metadata(self, table_name):
        sql = f"""
        SELECT max(last_updated) as last_updated FROM common.etl_metadata WHERE table_name = '{table_name}'
        """
        return self.pg_ops.read_data(sql)
    
        

    def sync_packaging(self):
        
        last_updated = self.get_metadata('logistics.packaging')
        last_updated_timstamp = last_updated[0].get('last_updated')
        if last_updated_timstamp:
            logging.info(f"Last updated time for packaging: {last_updated}")
        else:
            last_updated_timstamp = datetime.datetime.strptime('1900-01-01 00:00:00', '%Y-%m-%d %H:%M:%S')

        logging.info("Running packaging")
        current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        df_packaging = self.get_packaging(last_updated_timstamp)
        logging.info("Packaging data fetched successfully.")
        if df_packaging.empty:
            logging.info("No new packaging data found.")
            return
        sql_statements = self.create_upsert_sql(df_packaging, 'logistics.packaging', 'packaging_uid')
        logging.info("SQL statements created successfully.")
        for sql in sql_statements:
            self.pg_ops.write_data(sql)
        logging.info("Packaging data inserted successfully.")
        self.insert_metadata('logistics.packaging', current_time)
        logging.info("Packaging metadata inserted successfully.")
    
    def sync_product(self):
        '''
        This function is used to get the product data from the mongo db and insert it into the postgres db.
        '''
        logging.info("Running product")
        last_updated = self.get_metadata('logistics.product')
        last_updated_timstamp = last_updated[0].get('last_updated')
        current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        if last_updated_timstamp:
            logging.info(f"Last updated time for product: {last_updated}")
        else:
            last_updated_timstamp = datetime.datetime.strptime('1900-01-01 00:00:00', '%Y-%m-%d %H:%M:%S')

        
        df_product = self.get_product(last_updated_timstamp)

        if df_product.empty:
            logging.info("No new product data found.")
            return
        
        sql_statements = self.create_upsert_sql(df_product, 'logistics.product', 'product_uid')
        logging.info("SQL statements created successfully.")
        for sql in sql_statements:
            self.pg_ops.write_data(sql)
        logging.info("Product data inserted successfully.")
        self.insert_metadata('logistics.product', current_time)
        logging.info("Product metadata inserted successfully.")

    def sync_customers(self):
        logging.info("Running customers")
        last_updated = self.get_metadata('logistics.customers')
        last_updated_timstamp = last_updated[0].get('last_updated')
        current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        if last_updated_timstamp:
            logging.info(f"Last updated time for customers: {last_updated}")
        else:
            last_updated_timstamp = datetime.datetime.strptime('1900-01-01 00:00:00', '%Y-%m-%d %H:%M:%S')

        df_customer = self.get_customers(last_updated_timstamp)
        if df_customer.empty:
            logging.info("No new customer data found.")
            return
        sql_statements = self.create_upsert_sql(df_customer, 'logistics.customers', 'customer_uid')
        logging.info("SQL statements created successfully.")
        for sql in sql_statements:
            self.pg_ops.write_data(sql)
        logging.info("Customer data inserted successfully.")
        self.insert_metadata('logistics.customers', current_time)
        logging.info("Customer metadata inserted successfully.")

    def sync_employees(self):
        logging.info("Running employees")
        last_updated = self.get_metadata('logistics.employee')
        last_updated_timstamp = last_updated[0].get('last_updated')
        current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        if last_updated_timstamp:
            logging.info(f"Last updated time for employee: {last_updated}")
        else:
            last_updated_timstamp = datetime.datetime.strptime('1900-01-01 00:00:00', '%Y-%m-%d %H:%M:%S')

        df_employee = self.get_employees(last_updated_timstamp)
        if df_employee.empty:
            logging.info("No new employee data found.")
            return
        sql_statements = self.create_upsert_sql(df_employee, 'logistics.employee', 'employee_id')
        logging.info("SQL statements created successfully.")
        for sql in sql_statements:
            self.pg_ops.write_data(sql)
        logging.info("Employee data inserted successfully.")
        self.insert_metadata('logistics.employee', current_time)
        logging.info("Employee metadata inserted successfully.")

    def sync_operational_invoices(self):
        logging.info("Running operational invoices sync")

        last_updated = self.get_metadata('logistics.operational_invoices')
        last_updated_timestamp = last_updated[0].get('last_updated')
        current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        if last_updated_timestamp:
            logging.info(f"Last updated time for operational invoices: {last_updated}")
        else:
            last_updated_timestamp = datetime.datetime.strptime('1900-01-01 00:00:00', '%Y-%m-%d %H:%M:%S')
        df_invoices = self.get_operational_invoices(last_updated_timestamp)
        if df_invoices.empty:
            logging.info("No new operational invoices data found.")
            return

        sql_statements = self.create_upsert_sql(df_invoices, 'logistics.operational_invoices', ('invoice_id', 'invoice_number'))
        logging.info("SQL statements created successfully.")

        for sql in sql_statements:
            self.pg_ops.write_data(sql)

        logging.info("Operational invoices data inserted successfully.")
        self.insert_metadata('logistics.operational_invoices', current_time)
        logging.info("Operational invoices metadata updated successfully.")
    

    def sync_customer_orders(self):
        logging.info("Running customer orders")
        last_updated = self.get_metadata('logistics.customer_orders')
        last_updated_timstamp = last_updated[0].get('last_updated')
        if last_updated_timstamp:
            logging.info(f"Last updated time for customer orders: {last_updated}")
        else:
            last_updated_timstamp = datetime.datetime.strptime('1900-01-01 00:00:00', '%Y-%m-%d %H:%M:%S')

        current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        df_customer_order, df_customer_order_products = self.get_customer_order(last_updated_timstamp)
        if df_customer_order.empty:
            logging.info("No new customer order data found.")
            return
        sql_statements = self.create_upsert_sql(df_customer_order, 'logistics.customer_orders', 'customer_order_uid')
        logging.info("SQL statements created successfully.")
        for sql in sql_statements:
            self.pg_ops.write_data(sql)
        logging.info("Customer order data inserted successfully.")
        self.insert_metadata('logistics.customer_orders', current_time)
        logging.info("Customer order metadata inserted successfully.")

        logging.info("Running customer order products")
        last_updated = self.get_metadata('logistics.customer_order_products')
        last_updated_timstamp = last_updated[0].get('last_updated')
        if last_updated_timstamp:
            logging.info(f"Last updated time for customer order products: {last_updated}")
        else:
            last_updated_timstamp = datetime.datetime.strptime('1900-01-01 00:00:00', '%Y-%m-%d %H:%M:%S')

        if df_customer_order_products.empty:
            logging.info("No new customer order product data found.")
            return
        sql_statements = self.create_upsert_sql(df_customer_order_products, 'logistics.customer_order_products', ('customer_order_product_id', 'customer_order_uid'))
        logging.info("SQL statements created successfully.")
        for sql in sql_statements:
            self.pg_ops.write_data(sql)

        logging.info("Customer order product data inserted successfully.")
        self.insert_metadata('logistics.customer_order_products', current_time)
        logging.info("Customer order product metadata inserted successfully.")

    def sync_order_book(self):
        logging.info("Running order book")
        last_updated = self.get_metadata('logistics.order_book')
        last_updated_timstamp = last_updated[0].get('last_updated')
        if last_updated_timstamp:
            logging.info(f"Last updated time for order book: {last_updated}")
        else:
            last_updated_timstamp = datetime.datetime.strptime('1900-01-01 00:00:00', '%Y-%m-%d %H:%M:%S')

        current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        df_order_book, df_order_book_products = self.get_order_book(last_updated_timstamp)
        if df_order_book.empty:
            logging.info("No new order book data found.")
            return
        
        sql_statements = self.create_upsert_sql(df_order_book, 'logistics.order_book', 'order_book_uid')
        logging.info("SQL statements created successfully.")
        for sql in sql_statements:
            self.pg_ops.write_data(sql)

        logging.info("Order book data inserted successfully.")
        self.insert_metadata('logistics.order_book', current_time)
        logging.info("Order book metadata inserted successfully.")

        logging.info("Running order book products")
        last_updated = self.get_metadata('logistics.order_book_products')
        last_updated_timstamp = last_updated[0].get('last_updated')
        if last_updated_timstamp:
            logging.info(f"Last updated time for order book products: {last_updated}")
        else:
            last_updated_timstamp = datetime.datetime.strptime('1900-01-01 00:00:00', '%Y-%m-%d %H:%M:%S')
        
        if df_order_book_products.empty:
            logging.info("No new order book product data found.")
            return
        sql_statements = self.create_upsert_sql(df_order_book_products, 'logistics.order_book_products', 'order_book_product_id')
        logging.info("SQL statements created successfully.")
        for sql in sql_statements:
            self.pg_ops.write_data(sql)
        logging.info("Order book product data inserted successfully.")
        self.insert_metadata('logistics.order_book_products', current_time)
        logging.info("Order book product metadata inserted successfully.")
    
    def sync_order_book_batch(self):
        logging.info("Running order book batch")
        last_updated = self.get_metadata('logistics.order_book_batch')
        last_updated_timstamp = last_updated[0].get('last_updated')
        if last_updated_timstamp:
            logging.info(f"Last updated time for order book batch: {last_updated}")
        else:
            last_updated_timstamp = datetime.datetime.strptime('1900-01-01 00:00:00', '%Y-%m-%d %H:%M:%S')

        current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        df_order_book_batch = self.get_order_book_batch(last_updated_timstamp)
        if df_order_book_batch.empty:
            logging.info("No new order book batch data found.")
            return
        
        sql_statements = self.create_upsert_sql(df_order_book_batch, 'logistics.order_book_batch', 'order_book_batch_uid')
        logging.info("SQL statements created successfully.")
        for sql in sql_statements:
            self.pg_ops.write_data(sql)
        logging.info("Order book batch data inserted successfully.")
        self.insert_metadata('logistics.order_book_batch', current_time)
        logging.info("Order book batch metadata inserted successfully.")
    
    def sync_supplier(self):
        logging.info("Running supplier")
        last_updated = self.get_metadata('logistics.supplier')
        last_updated_timstamp = last_updated[0].get('last_updated')
        if last_updated_timstamp:
            logging.info(f"Last updated time for supplier: {last_updated}")
        else:
            last_updated_timstamp = datetime.datetime.strptime('1900-01-01 00:00:00', '%Y-%m-%d %H:%M:%S')

        current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        df_supplier, df_supplier_products = self.get_supplier(last_updated_timstamp)
        if df_supplier.empty:
            logging.info("No new supplier data found.")
            return
        sql_statements = self.create_upsert_sql(df_supplier, 'logistics.supplier', 'supplier_uid')
        logging.info("SQL statements created successfully.")
        for sql in sql_statements:
            self.pg_ops.write_data(sql)
        logging.info("Supplier data inserted successfully.")
        self.insert_metadata('logistics.supplier', current_time)
        logging.info("Supplier metadata inserted successfully.")

        logging.info("Running supplier products")
        last_updated = self.get_metadata('logistics.supplier_products')
        last_updated_timstamp = last_updated[0].get('last_updated')
        if last_updated_timstamp:
            logging.info(f"Last updated time for supplier products: {last_updated}")
        else:
            last_updated_timstamp = datetime.datetime.strptime('1900-01-01 00:00:00', '%Y-%m-%d %H:%M:%S')

        if df_supplier_products.empty:
            logging.info("No new supplier product data found.")
            return
        sql_statements = self.create_upsert_sql(df_supplier_products, 'logistics.supplier_products', 'supplier_product_uid')
        logging.info("SQL statements created successfully.")
        for sql in sql_statements:
            self.pg_ops.write_data(sql)
        logging.info("Supplier product data inserted successfully.")
        self.insert_metadata('logistics.supplier_products', current_time)
        logging.info("Supplier product metadata inserted successfully.")
    
    def sync_supplier_order_book(self):
        logging.info("Running supplier order book")
        last_updated = self.get_metadata('logistics.supplier_order_book')
        last_updated_timstamp = last_updated[0].get('last_updated')
        if last_updated_timstamp:
            logging.info(f"Last updated time for supplier order book: {last_updated}")
        else:
            last_updated_timstamp = datetime.datetime.strptime('1900-01-01 00:00:00', '%Y-%m-%d %H:%M:%S')
        
        current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        df_supplier_order_book, df_supplier_order_book_products = self.get_supplier_order_book(last_updated_timstamp)
        if df_supplier_order_book.empty:
            logging.info("No new supplier order book data found.")
            return
        sql_statements = self.create_upsert_sql(df_supplier_order_book, 'logistics.supplier_order_book', 'supplier_order_book_uid')
        logging.info("SQL statements created successfully.")
        for sql in sql_statements:
            self.pg_ops.write_data(sql)
        
        logging.info("Supplier order book data inserted successfully.")
        self.insert_metadata('logistics.supplier_order_book', current_time)
        logging.info("Supplier order book metadata inserted successfully.")

        logging.info("Running supplier order book products")
        last_updated = self.get_metadata('logistics.supplier_order_book_products')

        last_updated_timstamp = last_updated[0].get('last_updated')
        if last_updated_timstamp:
            logging.info(f"Last updated time for supplier order book products: {last_updated}")
        else:
            last_updated_timstamp = datetime.datetime.strptime('1900-01-01 00:00:00', '%Y-%m-%d %H:%M:%S')
        
        if df_supplier_order_book_products.empty:
            logging.info("No new supplier order book product data found.")
            return
        sql_statements = self.create_upsert_sql(df_supplier_order_book_products, 'logistics.supplier_order_book_products', 'supplier_order_book_product_id')
        logging.info("SQL statements created successfully.")
        for sql in sql_statements:
            self.pg_ops.write_data(sql)
        
        logging.info("Supplier order book product data inserted successfully.")
        self.insert_metadata('logistics.supplier_order_book_products', current_time)
        logging.info("Supplier order book product metadata inserted successfully.")
    
    def sync_supplier_order(self):
        logging.info("Running supplier order")
        last_updated = self.get_metadata('logistics.supplier_order')
        last_updated_timstamp = last_updated[0].get('last_updated')
        if last_updated_timstamp:
            logging.info(f"Last updated time for supplier order: {last_updated}")
        else:
            last_updated_timstamp = datetime.datetime.strptime('1900-01-01 00:00:00', '%Y-%m-%d %H:%M:%S')
        
        current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        df_supplier_order, df_supplier_order_products , df_supplier_products, df_supplier_product_packaging = self.get_supplier_order(last_updated_timstamp)
        if df_supplier_order.empty:
            logging.info("No new supplier order data found.")
            return
        sql_statements = self.create_upsert_sql(df_supplier_order, 'logistics.supplier_order', 'supplier_order_uid')
        logging.info("SQL statements created successfully.")
        for sql in sql_statements:
            self.pg_ops.write_data(sql)
        logging.info("Supplier order data inserted successfully.")
        self.insert_metadata('logistics.supplier_order', current_time)
        logging.info("Supplier order metadata inserted successfully.")
        logging.info("Running supplier order products")


        last_updated = self.get_metadata('logistics.supplier_order_products')
        last_updated_timstamp = last_updated[0].get('last_updated')
        if last_updated_timstamp:
            logging.info(f"Last updated time for supplier order products: {last_updated}")
        else:
            last_updated_timstamp = datetime.datetime.strptime('1900-01-01 00:00:00', '%Y-%m-%d %H:%M:%S')

        if df_supplier_order_products.empty:
            logging.info("No new supplier order product data found.")
            return
        sql_statements = self.create_upsert_sql(df_supplier_order_products, 'logistics.supplier_order_products', 'supplier_order_product_uid')
        logging.info("SQL statements created successfully.")
        for sql in sql_statements:
            self.pg_ops.write_data(sql)
        logging.info("Supplier order product data inserted successfully.")
        self.insert_metadata('logistics.supplier_order_products', current_time)
        logging.info("Supplier order product metadata inserted successfully.")

        logging.info("Running supplier products")


        last_updated = self.get_metadata('logistics.supplier_order_supplier_products')
        last_updated_timstamp = last_updated[0].get('last_updated')
        if last_updated_timstamp:
            logging.info(f"Last updated time for supplier products: {last_updated}")
        else:
            last_updated_timstamp = datetime.datetime.strptime('1900-01-01 00:00:00', '%Y-%m-%d %H:%M:%S')

        if not df_supplier_products.empty:
            logging.info("No new supplier product data found.")
            return
        sql_statements = self.create_upsert_sql(df_supplier_products, 'logistics.supplier_order_supplier_products', ('supplier_id', 'product_id'))
        logging.info("SQL statements created successfully.")
        for sql in sql_statements:
            self.pg_ops.write_data(sql)
        
        logging.info("Supplier product data inserted successfully.")
        self.insert_metadata('logistics.supplier_order_supplier_products', current_time)
        logging.info("Supplier product metadata inserted successfully.")

        logging.info("Running supplier product packaging")

        last_updated = self.get_metadata('logistics.supplier_order_product_packaging')
        last_updated_timstamp = last_updated[0].get('last_updated')
        if last_updated_timstamp:
            logging.info(f"Last updated time for supplier product packaging: {last_updated}")
        else:
            last_updated_timstamp = datetime.datetime.strptime('1900-01-01 00:00:00', '%Y-%m-%d %H:%M:%S')
        
        if df_supplier_product_packaging.empty:
            logging.info("No new supplier product packaging data found.")
            return
        sql_statements = self.create_upsert_sql(df_supplier_product_packaging, 'logistics.supplier_order_product_packaging', ('product_id', 'supplier_id'))
        logging.info("SQL statements created successfully.")
        for sql in sql_statements:
            self.pg_ops.write_data(sql)
        logging.info("Supplier product packaging data inserted successfully.")
        self.insert_metadata('logistics.supplier_order_product_packaging', current_time)
        logging.info("Supplier product packaging metadata inserted successfully.")
    
    def sync_product_batch(self):
        logging.info("Running product batch")
        last_updated = self.get_metadata('logistics.product_batch')
        last_updated_timstamp = last_updated[0].get('last_updated')
        if last_updated_timstamp:
            logging.info(f"Last updated time for product batch: {last_updated}")
           
        else:
            last_updated_timstamp = datetime.datetime.strptime('1900-01-01 00:00:00', '%Y-%m-%d %H:%M:%S')

        current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        df_product_batch = self.get_product_batch(last_updated_timstamp)
        if df_product_batch.empty:
            logging.info("No new product batch data found.")
            return
        sql_statements = self.create_upsert_sql(df_product_batch, 'logistics.product_batch', 'product_batch_uid')
        logging.info("SQL statements created successfully.")
        for sql in sql_statements:
            self.pg_ops.write_data(sql)
        logging.info("Product batch data inserted successfully.")
        self.insert_metadata('logistics.product_batch', current_time)
        logging.info("Product batch metadata inserted successfully.")

    def sync_users(self):
        logging.info("Running users")
        last_updated = self.get_metadata('logistics.users')
        last_updated_timstamp = last_updated[0].get('last_updated')
        if last_updated_timstamp:
            logging.info(f"Last updated time for users: {last_updated}")
        else:
            last_updated_timstamp = datetime.datetime.strptime('1900-01-01 00:00:00', '%Y-%m-%d %H:%M:%S')
        
        current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        df_user = self.get_users(last_updated_timstamp)
        if df_user.empty:
            logging.info("No new user data found.")
            return
        sql_statements = self.create_upsert_sql(df_user, 'logistics.users', 'user_uid')
        logging.info("SQL statements created successfully.")
        for sql in sql_statements:
            self.pg_ops.write_data(sql)
        logging.info("User data inserted successfully.")
        self.insert_metadata('logistics.users', current_time)
        logging.info("User metadata inserted successfully.")
    
    def sync_inventory(self):
        logging.info("Running inventory")
        last_updated = self.get_metadata('logistics.inventory_product')
        last_updated_timstamp = last_updated[0].get('last_updated')
        if last_updated_timstamp:
            logging.info(f"Last updated time for inventory: {last_updated}")
        else:
            last_updated_timstamp = datetime.datetime.strptime('1900-01-01 00:00:00', '%Y-%m-%d %H:%M:%S')
        
        current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        df_inventory = self.get_inventory_product(last_updated_timstamp)
        if df_inventory.empty:
            logging.info("No new inventory data found.")
            return
        sql_statements = self.create_upsert_sql(df_inventory, 'logistics.inventory_product', 'inventory_product_uid')
        logging.info("SQL statements created successfully.")
        for sql in sql_statements:
            self.pg_ops.write_data(sql)
        
        logging.info("Inventory data inserted successfully.")
        self.insert_metadata('logistics.inventory_product', current_time)
        logging.info("Inventory metadata inserted successfully.")
    
    def sync_inventory_product_transaction(self):
        logging.info("Running inventory product transaction")
        last_updated = self.get_metadata('logistics.inventory_product_transaction')
        last_updated_timstamp = last_updated[0].get('last_updated')
        if last_updated_timstamp:
            logging.info(f"Last updated time for inventory product transaction: {last_updated}")
        else:
            last_updated_timstamp = datetime.datetime.strptime('1900-01-01 00:00:00', '%Y-%m-%d %H:%M:%S')
        
        current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        df_inventory_product_transaction = self.get_inventory_product_transaction(last_updated_timstamp)
        if df_inventory_product_transaction.empty:
            logging.info("No new inventory product transaction data found.")
            return
        sql_statements = self.create_upsert_sql(df_inventory_product_transaction, 'logistics.inventory_product_transaction', 'inventory_product_transaction_uid')
        logging.info("SQL statements created successfully.")
        for sql in sql_statements:
            self.pg_ops.write_data(sql)
        
        logging.info("Inventory product transaction data inserted successfully.")
        self.insert_metadata('logistics.inventory_product_transaction', current_time)
        logging.info("Inventory product transaction metadata inserted successfully.")
    

    def sync_activity_log(self):
        logging.info("Running activity log")
        last_updated = self.get_metadata('logistics.activity')
        last_updated_timstamp = last_updated[0].get('last_updated')
        if last_updated_timstamp:
            logging.info(f"Last updated time for activity log: {last_updated}")
        else:
            last_updated_timstamp = datetime.datetime.strptime('1900-01-01 00:00:00', '%Y-%m-%d %H:%M:%S')

        current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        df_activity_log , df_activity_remarks = self.get_activity_log(last_updated_timstamp)
        if df_activity_log.empty:
            logging.info("No new activity log data found.")
            return
        sql_statements = self.create_upsert_sql(df_activity_log, 'logistics.activity', 'activity_id')
        logging.info("SQL statements created successfully.")
        for sql in sql_statements:
            self.pg_ops.write_data(sql)
        
        logging.info("Activity log data inserted successfully.")
        self.insert_metadata('logistics.activity', current_time)
        logging.info("Activity log metadata inserted successfully.")
        
        if df_activity_remarks.empty:
            logging.info("No new activity remarks data found.")
            return

        sql_statements = self.create_upsert_sql(df_activity_remarks, 'logistics.activity_remarks', ('activity_id', 'task_id', 'remark_type'))
        logging.info("SQL statements created successfully.")
        for sql in sql_statements:
            self.pg_ops.write_data(sql)
        
        logging.info("Activity remarks data inserted successfully.")
        self.insert_metadata('logistics.activity_remarks', current_time)
        logging.info("Activity remarks metadata inserted successfully.")
