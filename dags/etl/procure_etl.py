import sys,os
sys.path.insert(0, '/opt/airflow/dag_dependencies')
import pandas as pd
import logging
import datetime
from datetime import timedelta
from db.postgres_operations import PostgresOperations
from config.db_config import DatabaseConfig
from functools import partial
from datetime import datetime, timezone
from common.etl_utils import procure_etl_dtypes
from common.common import fix_dtype_mapping_for_pandas
from dotenv import load_dotenv

logging.basicConfig(level=logging.INFO)

load_dotenv()

class ProcureToReportingETL:
    def __init__(self):
        reporting_stack_postgres_params = DatabaseConfig.get_reporting_stack_postgres_params()
        procure_stack_postgres_params = DatabaseConfig.get_procuro_stack_postgres_params()
        self.source_conn = PostgresOperations(procure_stack_postgres_params)
        self.destination_conn = PostgresOperations(reporting_stack_postgres_params)
        self.exculed_columns = ['roles', 'pipeline_stages']
        self.all_times = [
            'last_status_change',
            'target_quotation_date',
            'po_delivery_time',
            'expiry_date',
            'banned_until',
            'approval_date',
            'mrd_date',
            'expected_delivery_date',
            'response_date',
            'last_active'
        ]
        self.timestamp_keys = {
            # Independent base/lookup tables (no foreign key dependencies)
            'business_units': 'created_at',
            'roles': 'created_at',
            'chemical_categories': 'created_at',
            
            # User and auth related (first level)
            # 'users': 'updated_at',
            
            # Tables dependent on users/base tables
            #'profiles': 'created_at',
            'dashboard_configs': 'updated_at',
            'audit_logs': 'timestamp',
            
            # Chemical hierarchy
            'chemicals': 'created_at',
            'chemical_applications': 'created_at',
            'chemical_safety_data': 'created_at',
            'chemical_synonyms': 'created_at',
            
            # Supplier hierarchy
            'suppliers': 'created_at',
            'supplier_contacts': 'created_at',
            'supplier_addresses': 'created_at',
            'supplier_financial_data': 'created_at',
            'supplier_operational_data': 'created_at',
            'supplier_documents': 'upload_date',
            
            # Supplier-Chemical relationships
            'supplier_chemicals': 'updated_at',
            'supplier_chemical_pricing': 'expiry_date',
            
            # Enquiry hierarchy
            'enquiries': 'last_status_change',
            'enquiry_documents': 'created_at',
            'enquiry_clarifications': 'created_at',
            'status_changes': 'created_at',
            'quote_preparations': 'created_at',
            # Quote preparation hierarchy
            'quote_preparation_details': 'created_at',
            'quotation_preparation_attachments': 'created_at',
            'quote_generation_details': 'generated_at',
            'quote_generation_options': 'updated_at',
            'quote_generation_attachments': 'created_at',
            
            # Quotation hierarchy
            'quotations': 'updated_at',
            'quotation_items': 'created_at',
            'quote_options': 'updated_at',
            'quote_attachments': 'created_at',
            'quote_approval_details': 'approved_at',
            'quotation_approvals': 'approval_date',
            'quotation_feedback': 'created_at',
            'quotation_feedback_attachments': 'created_at',
            
            # Supplier identification
            'supplier_identified_details': 'created_at',
            'supplier_identified_attachments': 'created_at',
            'supplier_quotation_attachments': 'created_at',
            
            # Sample hierarchy
            'samples': 'request_date',
            'sample_request_details': 'created_at',
            'sample_requests_attachments': 'created_at',
            'sample_initiation_details': 'initiated_at',
            'sample_test_results': 'test_date',
            'sample_feedback': 'created_at',
            'sample_feedback_attachments': 'created_at',
            
            # Purchase order hierarchy
            'purchase_orders': 'created_at',
            'purchase_orders_raised': 'created_at',
            'purchase_order_items': 'created_at',
            ##'purchase_order_attachments': 'created_at',
            'po_items': 'delivery_date',
            'po_deliveries': 'delivery_date',
            
            # Reports
            'saved_reports': 'created_at'
        }
    
    def get_source_data(self, schema, table_name, timestamp_key, last_updated_timestamp):
        
        query = f"SELECT * FROM {schema}.{table_name} WHERE cast({timestamp_key} as timestamp) > '{last_updated_timestamp}'"
        if timestamp_key == 'NULL':
            query = f"SELECT * FROM {schema}.{table_name}"
            
        df = pd.read_sql(query, self.source_conn.engine)
        
        # drop dict type columns
        for col in df.columns:
            if '_at' in col:
                df[col] = pd.to_datetime(df[col], utc=True)

        for col in self.all_times:
            if col in df.columns:
                df[col] = pd.to_datetime(df[col], utc=True)

        procure_all_columns = procure_etl_dtypes[table_name].keys()
        procure_dtypes = {k: v for k, v in procure_etl_dtypes[table_name].items() if v != 'datetime64[ns]'}
        procure_dtypes = fix_dtype_mapping_for_pandas(procure_dtypes)
        for cc in procure_all_columns:
            if cc not in df.columns:
                df[cc] = None
        df = df.astype(procure_dtypes)
                
        cols_to_drop = ['raw_app_meta_data','raw_user_meta_data']

        for col in cols_to_drop:
            if col in df.columns:
                df = df.drop(columns=[col], axis=1)
        logging.info(f"Source data fetched successfully for table: {table_name} Total records: {df.shape}")
        df = df[procure_all_columns]
        return df
    
    def create_temp_table(self, df, schema, table_name):
        temp_table_name = f"temp_{table_name}"
        self.destination_conn.write_data(f"DROP TABLE IF EXISTS common.{temp_table_name};")
        df.to_sql(temp_table_name, self.destination_conn.engine, if_exists='replace', schema='common', index=False)

        return True
    
    def upsert_data(self, source_schema, table_name, conflict_columns : list, df : pd.DataFrame):
        temp_table_name = f"temp_{table_name}"
        query = f"""
        INSERT INTO {source_schema}.{table_name} ({', '.join(df.columns)})
        SELECT * FROM common.{temp_table_name}
        ON CONFLICT ({', '.join(conflict_columns)})
        DO UPDATE SET
            {', '.join([f"{col} = EXCLUDED.{col}" for col in df.columns])};
        """
        self.destination_conn.write_data(query)
        logging.info(f"Data upserted successfully for table: {table_name}")
        logging.info(f"Dropping temp table for table: common.{table_name}")
        self.destination_conn.write_data(f"DROP TABLE IF EXISTS common.{temp_table_name};")
        logging.info(f"Temp table dropped successfully for table: {table_name}")
        
        return True
    
    def sync_auth_tables(self):
        logging.info("Syncing auth tables")
        table = 'users'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('auth','procurement', table, 'updated_at', ['id']):
            logging.info("No new data found for auth tables")
            return False
        self.insert_metadata('procurement.'+table, current_time)
        logging.info("Auth tables synced successfully")
        return True
    
    def get_last_updated_timestamp(self, table_name , schema = 'common'):
        sql = f"""
        SELECT max(last_updated) as last_updated FROM common.etl_metadata WHERE table_name = '{table_name}'
        """
        return self.destination_conn.read_data(sql)
    
    def insert_metadata(self, table_name, last_updated):
        logging.info(f"Inserting metadata for table: {table_name}")
        sql = f"""
        INSERT INTO common.etl_metadata (table_name, last_updated) 
        VALUES ('{table_name}', '{last_updated}')
        """
        self.destination_conn.write_data(sql)
        logging.info(f"Metadata inserted successfully for table: {table_name}")
        return True

    def write_to_db(self, df, table_name, schema='procurement'):
        logging.info(f"Writing data to table: {table_name}")
        df.to_sql(table_name, self.destination_conn.engine, if_exists='replace', schema=schema, index=False)
        logging.info(f"Data written successfully to table: {table_name}")
        return True
    
    def sync_data(self, source_schema, target_schema, table_name, timestamp_key, conflict_columns : list):
        last_updated = self.get_last_updated_timestamp(f'{target_schema}.{table_name}')
        last_updated_timestamp = last_updated[0].get('last_updated')
        if last_updated_timestamp:
            logging.info(f"Last updated time for packaging: {last_updated}")
        else:
            last_updated_timestamp = datetime.strptime('1900-01-01 00:00:00', '%Y-%m-%d %H:%M:%S')

        logging.info(f"Last updated timestamp for table: {target_schema}.{table_name} is {last_updated_timestamp}")
        
        logging.info(f"Fetching data for table: {table_name}")
        source_data = self.get_source_data(source_schema, table_name, timestamp_key, last_updated_timestamp)

        if source_data.empty:
            logging.info(f"No new data found for table: {table_name}")
            return False

        if timestamp_key=='NULL':
            self.destination_conn.write_data(f"TRUNCATE TABLE {target_schema}.{table_name} CASCADE")

        self.create_temp_table(source_data, 'common', table_name)
        
        logging.info(f"Upserting data for table: {table_name}")
        self.upsert_data(target_schema, table_name, conflict_columns, source_data)
        logging.info(f"Data upserted successfully for table: {table_name}")
        return True

    def sync_all_tables(self):
        logging.info("Syncing all tables")
        for table, timestamp_key in self.timestamp_keys.items():
            if table in self.exculed_columns:
                continue
            current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
            logging.info(f"Syncing table: {table}")
            is_data_available = self.sync_data('public','procurement', table, timestamp_key, ['id'])
            if not is_data_available:
                continue
            self.insert_metadata('procurement.'+table, current_time)
            logging.info(f"Table: {table} synced successfully")



    def sync_business_units(self):
        logging.info("Syncing business units")
        table = 'business_units'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','procurement', table, 'created_at', ['id']):
            logging.info("No new data found for business units")
            return False
        self.insert_metadata('procurement.'+table, current_time)
        logging.info("Business units synced successfully")
        return True

    def sync_roles(self):
        logging.info("Syncing roles")
        table = 'roles'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','procurement', table, 'created_at', ['id']):
            logging.info("No new data found for roles")
            return False
        self.insert_metadata('procurement.'+table, current_time)
        logging.info("Roles synced successfully")
        return True

    def sync_chemical_categories(self):
        logging.info("Syncing chemical categories") 
        table = 'chemical_categories'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','procurement', table, 'created_at', ['id']):
            logging.info("No new data found for chemical categories")
            return False
        self.insert_metadata('procurement.'+table, current_time)
        logging.info("Chemical categories synced successfully")
        return True

    def sync_dashboard_configs(self):
        logging.info("Syncing dashboard configs")
        table = 'dashboard_configs' 
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','procurement', table, 'updated_at', ['id']):
            logging.info("No new data found for dashboard configs")
            return False
        self.insert_metadata('procurement.'+table, current_time)
        logging.info("Dashboard configs synced successfully")
        return True
    
    def sync_audit_logs(self):
        logging.info("Syncing audit logs")
        table = 'audit_logs'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','procurement', table, 'timestamp', ['id']):
            logging.info("No new data found for audit logs")
            return False
        self.insert_metadata('procurement.'+table, current_time)
        logging.info("Audit logs synced successfully")
        return True

    def sync_chemicals(self):
        logging.info("Syncing chemicals")
        table = 'chemicals'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','procurement', table, 'created_at', ['id']):
            logging.info("No new data found for chemicals")
            return False
        self.insert_metadata('procurement.'+table, current_time)
        logging.info("Chemicals synced successfully")
        return True

    def sync_chemical_applications(self):
        logging.info("Syncing chemical applications")
        table = 'chemical_applications'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','procurement', table, 'created_at', ['id']):
            logging.info("No new data found for chemical applications")
            return False
        self.insert_metadata('procurement.'+table, current_time)
        logging.info("Chemical applications synced successfully")
        return True

    def sync_chemical_safety_data(self):
        logging.info("Syncing chemical safety data")
        table = 'chemical_safety_data'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','procurement', table, 'created_at', ['id']):
            logging.info("No new data found for chemical safety data")
            return False
        self.insert_metadata('procurement.'+table, current_time)
        logging.info("Chemical safety data synced successfully")
        return True

    def sync_chemical_synonyms(self):
        logging.info("Syncing chemical synonyms")
        table = 'chemical_synonyms'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','procurement', table, 'created_at', ['id']):
            logging.info("No new data found for chemical synonyms")
            return False
        self.insert_metadata('procurement.'+table, current_time)
        logging.info("Chemical synonyms synced successfully")
        return True
    
    def sync_suppliers(self):
        logging.info("Syncing suppliers")
        table = 'suppliers'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','procurement', table, 'created_at', ['id']):
            logging.info("No new data found for suppliers")
            return False
        self.insert_metadata('procurement.'+table, current_time)
        logging.info("Suppliers synced successfully")
        return True
    
    def sync_supplier_contacts(self):
        logging.info("Syncing supplier contacts")
        table = 'supplier_contacts'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','procurement', table, 'created_at', ['id']):
            logging.info("No new data found for supplier contacts")
            return False
        self.insert_metadata('procurement.'+table, current_time)
        logging.info("Supplier contacts synced successfully")
        return True
    
    def sync_supplier_addresses(self):
        logging.info("Syncing supplier addresses")
        table = 'supplier_addresses'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','procurement', table, 'created_at', ['id']):
            logging.info("No new data found for supplier addresses")
            return False
        self.insert_metadata('procurement.'+table, current_time)
        logging.info("Supplier addresses synced successfully")
        return True
    def sync_supplier_financial_data(self):
        logging.info("Syncing supplier financial data")
        table = 'supplier_financial_data'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','procurement', table, 'created_at', ['id']):
            logging.info("No new data found for supplier financial data")
            return False
        self.insert_metadata('procurement.'+table, current_time)
        logging.info("Supplier financial data synced successfully")
        return True
    def sync_supplier_operational_data(self):
        logging.info("Syncing supplier operational data")
        table = 'supplier_operational_data'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','procurement', table, 'created_at', ['id']):
            logging.info("No new data found for supplier operational data")
            return False
        self.insert_metadata('procurement.'+table, current_time)
        logging.info("Supplier operational data synced successfully")
        return True
    
    def sync_supplier_documents(self):
        logging.info("Syncing supplier documents")
        table = 'supplier_documents'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','procurement', table, 'upload_date', ['id']):
            logging.info("No new data found for supplier documents")
            return False
        self.insert_metadata('procurement.'+table, current_time)
        logging.info("Supplier documents synced successfully")
        return True
    
    def sync_supplier_chemicals(self):
        logging.info("Syncing supplier chemicals")
        table = 'supplier_chemicals'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','procurement', table, 'updated_at', ['id']):
            logging.info("No new data found for supplier chemicals")
            return False
        self.insert_metadata('procurement.'+table, current_time)
        logging.info("Supplier chemicals synced successfully")
        return True
    
    def sync_supplier_chemical_pricing(self):
        logging.info("Syncing supplier chemical pricing")
        table = 'supplier_chemical_pricing'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','procurement', table, 'expiry_date', ['id']):
            logging.info("No new data found for supplier chemical pricing")
            return False
        self.insert_metadata('procurement.'+table, current_time)
        logging.info("Supplier chemical pricing synced successfully")
        return True
    
    def sync_enquiries(self):
        logging.info("Syncing enquiries")
        table = 'enquiries'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','procurement', table, 'last_status_change', ['id']):
            logging.info("No new data found for enquiries")
            return False
        self.insert_metadata('procurement.'+table, current_time)
        logging.info("Enquiries synced successfully")
        return True
    
    def sync_enquiry_documents(self):
        logging.info("Syncing enquiry documents")
        table = 'enquiry_documents'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','procurement', table, 'created_at', ['id']):
            logging.info("No new data found for enquiry documents")
            return False
        self.insert_metadata('procurement.'+table, current_time)
        logging.info("Enquiry documents synced successfully")
        return True
    
    def sync_enquiry_clarifications(self):
        logging.info("Syncing enquiry clarifications")
        table = 'enquiry_clarifications'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','procurement', table, 'created_at', ['id']):
            logging.info("No new data found for enquiry clarifications")
            return False
        self.insert_metadata('procurement.'+table, current_time)
        logging.info("Enquiry clarifications synced successfully")
        return True
    
    def sync_status_changes(self):
        logging.info("Syncing status changes")
        table = 'status_changes'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','procurement', table, 'created_at', ['id']):
            logging.info("No new data found for status changes")
            return False
        self.insert_metadata('procurement.'+table, current_time)
        logging.info("Status changes synced successfully")
        return True
    
    def sync_quote_preparations(self):
        logging.info("Syncing quote preparations")
        table = 'quote_preparations'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','procurement', table, 'created_at', ['id']):
            logging.info("No new data found for quote preparations")
            return False
        self.insert_metadata('procurement.'+table, current_time)
        logging.info("Quote preparations synced successfully")
        return True 
    
    def sync_quote_preparation_details(self):
        logging.info("Syncing quote preparation details")
        table = 'quote_preparation_details'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','procurement', table, 'created_at', ['id']):
            logging.info("No new data found for quote preparation details")
            return False
        self.insert_metadata('procurement.'+table, current_time)
        logging.info("Quote preparation details synced successfully")
        return True
    
    def sync_quotation_preparation_attachments(self):
        logging.info("Syncing quotation preparation attachments")
        table = 'quotation_preparation_attachments'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','procurement', table, 'created_at', ['id']):
            logging.info("No new data found for quotation preparation attachments")
            return False
        self.insert_metadata('procurement.'+table, current_time)
        logging.info("Quotation preparation attachments synced successfully")
        return True
    
    def sync_quote_generation_details(self):
        logging.info("Syncing quote generation details")
        table = 'quote_generation_details'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','procurement', table, 'generated_at', ['id']):
            logging.info("No new data found for quote generation details")
            return False
        
        self.insert_metadata('procurement.'+table, current_time)
        logging.info("Quote generation details synced successfully")
        return True
    
    def sync_quote_generation_options(self):
        logging.info("Syncing quote generation options")
        table = 'quote_generation_options'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','procurement', table, 'updated_at', ['id']):
            logging.info("No new data found for quote generation options")
            return False
        self.insert_metadata('procurement.'+table, current_time)
        logging.info("Quote generation options synced successfully")
        return True
    
    def sync_quote_generation_attachments(self):
        logging.info("Syncing quote generation attachments")
        table = 'quote_generation_attachments'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','procurement', table, 'created_at', ['id']):
            logging.info("No new data found for quote generation attachments")
            return False
        self.insert_metadata('procurement.'+table, current_time)
        logging.info("Quote generation attachments synced successfully")
        return True
    
    def sync_quotations(self):
        logging.info("Syncing quotations")
        table = 'quotations'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','procurement', table, 'updated_at', ['id']):
            logging.info("No new data found for quotations")
            return False
        self.insert_metadata('procurement.'+table, current_time)
        logging.info("Quotations synced successfully")
        return True 


    def sync_quotation_items(self):
        logging.info("Syncing quotation items")
        table = 'quotation_items'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','procurement', table, 'created_at', ['id']):
            logging.info("No new data found for quotation items")
            return False
        self.insert_metadata('procurement.'+table, current_time)
        logging.info("Quotation items synced successfully")
        return True
    
    def sync_quote_options(self):
        logging.info("Syncing quote options")
        table = 'quote_options'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','procurement', table, 'updated_at', ['id']):
            logging.info("No new data found for quote options")
            return False
        self.insert_metadata('procurement.'+table, current_time)
        logging.info("Quote options synced successfully")
        return True
    
    def sync_quote_attachments(self):
        logging.info("Syncing quote attachments")
        table = 'quote_attachments'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','procurement', table, 'created_at', ['id']):
            logging.info("No new data found for quote attachments")
            return False
        self.insert_metadata('procurement.'+table, current_time)
        logging.info("Quote attachments synced successfully")
        return True
    
    def sync_quote_approval_details(self):
        logging.info("Syncing quote approval details")
        table = 'quote_approval_details'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','procurement', table, 'approved_at', ['id']):
            logging.info("No new data found for quote approval details")
            return False
        self.insert_metadata('procurement.'+table, current_time)
        logging.info("Quote approval details synced successfully")
        return True
    
    def sync_quotation_approvals(self):
        logging.info("Syncing quotation approvals")
        table = 'quotation_approvals'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','procurement', table, 'approval_date', ['id']):
            logging.info("No new data found for quotation approvals")
            return False
        self.insert_metadata('procurement.'+table, current_time)
        logging.info("Quotation approvals synced successfully")
        return True
    
    def sync_quotation_feedback(self):
        logging.info("Syncing quotation feedback")
        table = 'quotation_feedback'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','procurement', table, 'created_at', ['id']):
            logging.info("No new data found for quotation feedback")
            return False
        self.insert_metadata('procurement.'+table, current_time)
        logging.info("Quotation feedback synced successfully")
        return True
    
    def sync_quotation_feedback_attachments(self):
        logging.info("Syncing quotation feedback attachments")
        table = 'quotation_feedback_attachments'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','procurement', table, 'created_at', ['id']):
            logging.info("No new data found for quotation feedback attachments")
            return False
        self.insert_metadata('procurement.'+table, current_time)
        logging.info("Quotation feedback attachments synced successfully")
        return True
    
    def sync_supplier_identified_details(self):
        logging.info("Syncing supplier identified details")
        table = 'supplier_identified_details'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','procurement', table, 'created_at', ['id']):
            logging.info("No new data found for supplier identified details")
            return False
        self.insert_metadata('procurement.'+table, current_time)
        logging.info("Supplier identified details synced successfully")
        return True
    
    def sync_supplier_identified_attachments(self):
        logging.info("Syncing supplier identified attachments")
        table = 'supplier_identified_attachments'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','procurement', table, 'created_at', ['id']):
            logging.info("No new data found for supplier identified attachments")
            return False
        
        self.insert_metadata('procurement.'+table, current_time)
        logging.info("Supplier identified attachments synced successfully")
        return True
    
    def sync_supplier_quotation_attachments(self):
        logging.info("Syncing supplier quotation attachments")
        table = 'supplier_quotation_attachments'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','procurement', table, 'created_at', ['id']):
            logging.info("No new data found for supplier quotation attachments")
            return False
        self.insert_metadata('procurement.'+table, current_time)
        logging.info("Supplier quotation attachments synced successfully")
        return True
    
    def sync_supplier_quotation_feedback(self):
        logging.info("Syncing supplier quotation feedback")
        table = 'supplier_quotation_feedback'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','procurement', table, 'created_at', ['id']):
            logging.info("No new data found for supplier quotation feedback")
            return False
        self.insert_metadata('procurement.'+table, current_time)
        logging.info("Supplier quotation feedback synced successfully")
        return True
    
    def sync_supplier_quotation_feedback_attachments(self):
        logging.info("Syncing supplier quotation feedback attachments")
        table = 'supplier_quotation_feedback_attachments'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','procurement', table, 'created_at', ['id']):
            logging.info("No new data found for supplier quotation feedback attachments")
            return False
        self.insert_metadata('procurement.'+table, current_time)
        logging.info("Supplier quotation feedback attachments synced successfully")
        return True
    
    def sync_samples(self):
        logging.info("Syncing samples")
        table = 'samples'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','procurement', table, 'request_date', ['id']):
            logging.info("No new data found for samples")
            return False
        self.insert_metadata('procurement.'+table, current_time)
        logging.info("Samples synced successfully")
        return True
    
    def sync_sample_request_details(self):
        logging.info("Syncing sample request details")
        table = 'sample_requests'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','procurement', table, 'last_status_change', ['id']):
            logging.info("No new data found for sample request details")
            return False
        self.insert_metadata('procurement.'+table, current_time)
        logging.info("Sample request details synced successfully")
        return True
    
    def sync_sample_status_history(self):
        logging.info("Syncing sample status history")
        table = 'sample_status_history'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','procurement', table, 'changed_at', ['id']):    
            logging.info("No new data found for sample status history")
            return False
        self.insert_metadata('procurement.'+table, current_time)
        logging.info("Sample status history synced successfully")
        return True
    
    def sync_sample_requests_attachments(self):
        logging.info("Syncing sample requests attachments")
        table = 'sample_requests_attachments'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','procurement', table, 'created_at', ['id']):
            logging.info("No new data found for sample requests attachments")
            return False
        self.insert_metadata('procurement.'+table, current_time)
        logging.info("Sample requests attachments synced successfully")
        return True
    
    def sync_sample_initiation_details(self):
        logging.info("Syncing sample initiation details")
        table = 'sample_initiation_details'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','procurement', table, 'initiated_at', ['id']):
            logging.info("No new data found for sample initiation details")
            return False
        self.insert_metadata('procurement.'+table, current_time)
        logging.info("Sample initiation details synced successfully")
        return True
    
    def sync_sample_test_results(self):
        logging.info("Syncing sample test results")
        table = 'sample_test_results'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','procurement', table, 'test_date', ['id']):
            logging.info("No new data found for sample test results")
            return False
        self.insert_metadata('procurement.'+table, current_time)
        logging.info("Sample test results synced successfully")
        return True
    
    def sync_sample_feedback(self):
        logging.info("Syncing sample feedback")
        table = 'sample_feedback'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','procurement', table, 'created_at', ['id']):
            logging.info("No new data found for sample feedback")
            return False
        self.insert_metadata('procurement.'+table, current_time)
        logging.info("Sample feedback synced successfully")
        return True
    
    def sync_sample_feedback_attachments(self):
        logging.info("Syncing sample feedback attachments")
        table = 'sample_feedback_attachments'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','procurement', table, 'created_at', ['id']):
            logging.info("No new data found for sample feedback attachments")
            return False
        self.insert_metadata('procurement.'+table, current_time)
        logging.info("Sample feedback attachments synced successfully")
        return True
    
    def sync_purchase_orders(self):
        logging.info("Syncing purchase orders")
        table = 'purchase_orders'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','procurement', table, 'created_at', ['id']):
            logging.info("No new data found for purchase orders")
            return False
        self.insert_metadata('procurement.'+table, current_time)
        logging.info("Purchase orders synced successfully")
        return True
    
    def sync_purchase_orders_raised(self):
        logging.info("Syncing purchase orders raised")
        table = 'purchase_orders_raised'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','procurement', table, 'created_at', ['id']):
            logging.info("No new data found for purchase orders raised")
            return False
        self.insert_metadata('procurement.'+table, current_time)
        logging.info("Purchase orders raised synced successfully")
        return True
    
    def sync_po_items(self):
        logging.info("Syncing po items")
        table = 'po_items'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','procurement', table, 'delivery_date', ['id']):
            logging.info("No new data found for po items")
            return False
        self.insert_metadata('procurement.'+table, current_time)
        logging.info("Po items synced successfully")
        return True
    
    def sync_po_deliveries(self):
        logging.info("Syncing po deliveries")
        table = 'po_deliveries'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','procurement', table, 'delivery_date', ['id']):
            logging.info("No new data found for po deliveries")
            return False
        self.insert_metadata('procurement.'+table, current_time)
        logging.info("Po deliveries synced successfully")
        return True
    def sync_profiles(self):
        logging.info("Syncing profiles")
        table = 'profiles'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','procurement', table, 'last_active', ['id']):
            logging.info("No new data found for profiles")
            return False
        self.insert_metadata('procurement.'+table, current_time)
        logging.info("Profiles synced successfully")
        return True
    def sync_saved_reports(self):
        logging.info("Syncing saved reports")
        table = 'saved_reports'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','procurement', table, 'created_at', ['id']):
            logging.info("No new data found for saved reports")
            return False
        self.insert_metadata('procurement.'+table, current_time)
        logging.info("Saved reports synced successfully")
        return True
    
    def sync_purchase_order_items(self):
        logging.info("Syncing purchase order items")
        table = 'purchase_order_items'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','procurement', table, 'created_at', ['id']):
            logging.info("No new data found for purchase order items")
            return False
        self.insert_metadata('procurement.'+table, current_time)
        logging.info("Purchase order items synced successfully")
        return True
    
