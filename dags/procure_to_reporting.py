import sys,os
sys.path.insert(0, '/opt/airflow/dag_dependencies')
from dotenv import load_dotenv
import pandas as pd
import logging
import datetime
from datetime import timedelta
from airflow import DAG
from airflow.operators.python import PythonOperator
from config.default import DEFAULT_DAG_ARGS
from functools import partial
from datetime import datetime, timezone
from etl.procure_etl import ProcureToReportingETL

logging.basicConfig(level=logging.INFO)

load_dotenv()


dag = DAG(
    dag_id='procure_to_reporting_dag',
    default_args={**DEFAULT_DAG_ARGS},
    description='Sync data from ProcureStack to PostgreSQL',
    schedule='0 * * * *',  # Runs every hour (at minute 0)
    catchup=False,
    tags=['database', 'sync', 'postgres', 'procure'],
    dagrun_timeout=timedelta(minutes=30),
    max_active_runs=1,
    start_date=datetime(2024, 1, 1, tzinfo=timezone.utc),
)

def sync_data(sync_function):
    """Generic sync function that creates MongoETL instance and calls specified sync method"""
    procure_etl = ProcureToReportingETL()
    getattr(procure_etl, sync_function)()


sync_tasks = [
    # Independent base/lookup tables
    ('business_units', 'sync_business_units'),
    # ('roles', 'sync_roles'),
    ('chemical_categories', 'sync_chemical_categories'),
    
    # Auth and dashboard related
    ('auth_tables', 'sync_auth_tables'),
    ('dashboard_configs', 'sync_dashboard_configs'),
    ('audit_logs', 'sync_audit_logs'),
    
    # Chemical hierarchy
    ('chemicals', 'sync_chemicals'),
    ('chemical_applications', 'sync_chemical_applications'),
    ('chemical_safety_data', 'sync_chemical_safety_data'),
    ('chemical_synonyms', 'sync_chemical_synonyms'),
    
    # Supplier hierarchy
    ('suppliers', 'sync_suppliers'),
    ('supplier_contacts', 'sync_supplier_contacts'),
    ('supplier_addresses', 'sync_supplier_addresses'),
    ('supplier_financial_data', 'sync_supplier_financial_data'),
    ('supplier_operational_data', 'sync_supplier_operational_data'),
    ('supplier_documents', 'sync_supplier_documents'),
    
    # Supplier-Chemical relationships
    ('supplier_chemicals', 'sync_supplier_chemicals'),
    ('supplier_chemical_pricing', 'sync_supplier_chemical_pricing'),
    
    
     # Enquiry and Quote hierarchy
    ('enquiries', 'sync_enquiries'),
    ('enquiry_documents', 'sync_enquiry_documents'),
    ('enquiry_clarifications', 'sync_enquiry_clarifications'),
    ('status_changes', 'sync_status_changes'),
    ('quote_preparations', 'sync_quote_preparations'),
    ('quote_preparation_details', 'sync_quote_preparation_details'),
    ('quotation_preparation_attachments', 'sync_quotation_preparation_attachments'),
    ('quote_generation_details', 'sync_quote_generation_details'),
    ('quote_generation_options', 'sync_quote_generation_options'),
    ('quote_generation_attachments', 'sync_quote_generation_attachments'),
    
    # Quotation hierarchy
    ('quotations', 'sync_quotations'),
    ('quotation_items', 'sync_quotation_items'),
    ('quote_options', 'sync_quote_options'),
    ('quote_attachments', 'sync_quote_attachments'),
    ('quote_approval_details', 'sync_quote_approval_details'),
    ('quotation_approvals', 'sync_quotation_approvals'),
    ('quotation_feedback', 'sync_quotation_feedback'),
    ('quotation_feedback_attachments', 'sync_quotation_feedback_attachments'),
    
    # Supplier identification
    ('supplier_identified_details', 'sync_supplier_identified_details'),
    ('supplier_identified_attachments', 'sync_supplier_identified_attachments'),
    ('supplier_quotation_attachments', 'sync_supplier_quotation_attachments'),
    
    # Sample hierarchy
    ('samples', 'sync_samples'),
    ('sample_request_details', 'sync_sample_request_details'),
    ('sample_requests_attachments', 'sync_sample_requests_attachments'),
    ('sample_initiation_details', 'sync_sample_initiation_details'),
    ('sample_test_results', 'sync_sample_test_results'),
    ('sample_feedback', 'sync_sample_feedback'),
    ('sample_feedback_attachments', 'sync_sample_feedback_attachments'),
    
    # Purchase order hierarchy
    ('purchase_orders', 'sync_purchase_orders'),
    ('purchase_orders_raised', 'sync_purchase_orders_raised'),
    ('purchase_order_items', 'sync_purchase_order_items'),
    ('po_items', 'sync_po_items'),
    ('po_deliveries', 'sync_po_deliveries'),
    
    # Profiles
    ('profiles', 'sync_profiles'),
    # Reports
    ('saved_reports', 'sync_saved_reports'),
    ('sample_status_history', 'sync_sample_status_history')
]

# Create tasks dynamically
tasks = {}
for task_name, sync_function in sync_tasks:
    tasks[task_name] = PythonOperator(
        task_id=f'sync_{task_name}',
        python_callable=partial(sync_data, sync_function),
        dag=dag,
        execution_timeout=timedelta(minutes=5),
    )

for i in range(len(sync_tasks) - 1):
    tasks[sync_tasks[i][0]] >> tasks[sync_tasks[i + 1][0]]